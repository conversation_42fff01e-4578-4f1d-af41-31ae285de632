/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Metadata, Viewport } from "next";


import TanstackProvider from "@/components/providers/tanstack-query-provider";
import { ThemeLanguageProvider } from "@/components/providers/theme-language-provider";
import { ThemeScript } from "@/components/theme-script";
import "@/styles/globals.css";
import { Toaster } from "@/components/ui/sonner";
import AppContext from "@/components/contexts/app-context";

export const metadata: Metadata = {
  title: "LoomRun 丨编织代码，一键运行",
  description:
    "当编织美学遇上暴力效率 - LoomRun重新定义开发生产力。AI驱动的代码编织术，让创意瞬间成型，让部署一键完成。体验数字世界的织梦术师力量。",
  openGraph: {
    title: "LoomRun 丨编织代码，一键运行",
    description:
      "当编织美学遇上暴力效率 - 重新定义开发生产力。前三天编织逻辑，后三天运行万物，第七天笑看众生织就数字宇宙。",
    url: "https://loomrun.top",
    siteName: "LoomRun",
    images: [
      {
        url: "https://loomrun.top/banner.png",
        width: 1200,
        height: 630,
        alt: "LoomRun Open Graph Image",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    title: "LoomRun 丨编织代码，一键运行",
    description:
      "当编织美学遇上暴力效率 - 重新定义开发生产力。体验数字世界的织梦术师力量。",
    images: ["https://loomrun.top/banner.png"],
    creator: "@loomrun",
  },
  metadataBase: new URL("https://loomrun.top"),
  applicationName: "LoomRun",
  appleWebApp: {
    capable: true,
    title: "LoomRun",
    statusBarStyle: "black-translucent",
  },
  icons: {
    icon: [
      { url: "/loomrun_icon.svg", type: "image/svg+xml" },
      { url: "/favicon_io/favicon.ico" },
      { url: "/favicon_io/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon_io/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/favicon_io/android-chrome-192x192.png", sizes: "192x192", type: "image/png" },
      { url: "/favicon_io/android-chrome-512x512.png", sizes: "512x512", type: "image/png" },
    ],
    shortcut: "/favicon_io/favicon.ico",
    apple: "/favicon_io/apple-touch-icon.png",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#f5f3f0",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <head>
        <ThemeScript />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
        <link rel="icon" type="image/svg+xml" href="/loomrun_icon.svg" />
        <link rel="icon" type="image/x-icon" href="/favicon_io/favicon.ico" />
        <link rel="icon" type="image/png" sizes="512x512" href="/favicon_io/android-chrome-512x512.png" />
        <link rel="icon" type="image/png" sizes="192x192" href="/favicon_io/android-chrome-192x192.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon_io/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon_io/favicon-16x16.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/favicon_io/apple-touch-icon.png" />
        <link rel="manifest" href="/favicon_io/site.webmanifest" />
      </head>
      <body
        className="antialiased bg-background min-h-screen"
        style={{ fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif' }}
        suppressHydrationWarning={true}
      >
        <Toaster richColors position="bottom-center" />
        <TanstackProvider>
          <ThemeLanguageProvider>
            <AppContext>{children}</AppContext>
          </ThemeLanguageProvider>
        </TanstackProvider>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 移除Next.js开发工具
              function removeDevTools() {
                const selectors = [
                  '[data-nextjs-dialog-overlay]',
                  '[data-nextjs-dialog]',
                  '[data-nextjs-dialog-left-right]',
                  '[data-nextjs-dialog-backdrop]',
                  'div[id^="__next-dev-indicator"]',
                  'div[data-nextjs-dev-overlay]',
                  '.__next-dev-indicator',
                  '.__next-dev-overlay',
                  '[class*="__next-dev"]',
                  '[class*="nextjs-dev"]',
                  '[data-nextjs-dev]'
                ];
                
                selectors.forEach(selector => {
                  const elements = document.querySelectorAll(selector);
                  elements.forEach(el => {
                    // 确保不删除Popover相关元素
                    if (!el.closest('[data-radix-portal]') && !el.closest('[data-radix-popper-content-wrapper]')) {
                      el.style.display = 'none';
                      el.style.visibility = 'hidden';
                      el.style.opacity = '0';
                      el.style.pointerEvents = 'none';
                      el.remove();
                    }
                  });
                });
                
                // 移除高z-index的固定定位元素（但排除Popover）
                const allFixedElements = document.querySelectorAll('div[style*="position: fixed"]');
                allFixedElements.forEach(el => {
                  const style = el.getAttribute('style') || '';
                  // 确保不删除Popover或其他重要的UI元素
                  if (style.includes('z-index') && (
                    style.includes('2147483647') || 
                    style.includes('999999') || 
                    style.includes('99999')
                  ) && !el.closest('[data-radix-popper-content-wrapper]') && 
                      !el.closest('[data-radix-portal]') && 
                      !el.hasAttribute('data-radix-popover-content') &&
                      !el.querySelector('[data-radix-popover-content]')) {
                    el.remove();
                  }
                });
              }
              
              // 立即执行
              removeDevTools();
              
              // 定期检查并移除
              setInterval(removeDevTools, 100);
              
              // LoomRun 品牌哲学彩蛋
              console.log('%c🚀 LoomRun - 当编织美学遇上暴力效率', 'color: #2563EB; font-size: 16px; font-weight: bold;');
              console.log('%c编织逻辑，运行万物 - 您正在使用数字世界的织梦术', 'color: #7DD3FC; font-size: 12px;');
              console.log('%c输入 loom.run() 查看隐藏功能 ✨', 'color: #F97316; font-size: 10px;');
              
              // 添加全局彩蛋函数
              window.loom = {
                run: () => {
                  console.log('%c🌌 织梦术师模式已激活', 'color: #4ADE80; font-size: 14px; font-weight: bold;');
                  console.log('%c前三天编织逻辑（Loom），后三天运行万物（Run）', 'color: #7DD3FC;');
                  console.log('%c而第七天... 您以天尊之姿，笑看众生用此域织就自己的宇宙', 'color: #F97316;');
                  return '🎭 欢迎来到 LoomRun 的数字纪元';
                }
              };
              
              // DOM变化时执行
              if (typeof MutationObserver !== 'undefined') {
                const observer = new MutationObserver(removeDevTools);
                observer.observe(document.body, { 
                  childList: true, 
                  subtree: true 
                });
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
