"use client";
import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X, Loader2 } from "lucide-react";
import { CommunityCard } from "./community-card";
import { PreviewModal } from "./preview-modal";

interface CommunityProject {
  id: number;
  originalProjectId: number;
  userId: number;
  title: string;
  htmlContent: string;
  createdAt: string;
  updatedAt: string;
  author: {
    name: string;
    email: string;
  };
}

interface CommunityModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenProject: (project: CommunityProject) => void;
}

export function CommunityModal({ isOpen, onClose, onOpenProject }: CommunityModalProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [projects, setProjects] = useState<CommunityProject[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [previewProject, setPreviewProject] = useState<CommunityProject | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // 搜索项目
  const searchProjects = useCallback(async (search: string = "") => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/community/projects?search=${encodeURIComponent(search)}&limit=20`);
      if (response.ok) {
        const data = await response.json();
        setProjects(data.projects || []);
      }
    } catch (error) {
      console.error('搜索社区项目失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isOpen) {
        searchProjects(searchTerm);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, isOpen, searchProjects]);

  // 打开时初始加载
  useEffect(() => {
    if (isOpen) {
      searchProjects();
    }
  }, [isOpen, searchProjects]);

  const handlePreview = useCallback((project: CommunityProject) => {
    setPreviewProject(project);
    setIsPreviewOpen(true);
  }, []);

  const handleOpenProject = useCallback((project: CommunityProject) => {
    onOpenProject(project);
    onClose();
  }, [onOpenProject, onClose]);

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-6xl w-full h-[80vh] bg-background border-border p-0">
          <DialogHeader className="p-6 border-b border-border">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-xl font-semibold text-foreground">
                LoomRun 社区
              </DialogTitle>
              <Button
                onClick={onClose}
                variant="outline"
                size="sm"
                className="bg-background border-border text-foreground hover:bg-accent hover:text-accent-foreground"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            
            {/* 搜索框 */}
            <div className="relative mt-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="搜索社区项目..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-background border-border text-foreground placeholder:text-muted-foreground focus:border-ring"
              />
            </div>
          </DialogHeader>

          {/* 项目列表 */}
          <div className="flex-1 overflow-y-auto p-6 bg-background">
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
              </div>
            ) : projects.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {projects.map((project, index) => (
          <CommunityCard
            key={`${project.id}-${index}`}
                    project={project}
                    onPreview={handlePreview}
                    onOpen={handleOpenProject}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <p>
                  {searchTerm ? `没有找到匹配 "${searchTerm}" 的项目` : '暂无社区项目'}
                </p>
                <p className="text-sm mt-2">
                  {searchTerm ? '尝试其他关键词' : '成为第一个分享项目的开发者！'}
                </p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* 预览模态框 */}
      <PreviewModal
        project={previewProject}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        onOpen={handleOpenProject}
      />
    </>
  );
} 