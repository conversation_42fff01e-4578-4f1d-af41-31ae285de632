"use client";

import React, { useState, useRef, useEffect } from 'react';
import { X, Send, Image, Smile } from 'lucide-react';

interface ContactChatProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

interface RecentMessage {
  id: string;
  preview: string;
  sender: string;
  timestamp: Date;
  unread?: boolean;
}

export default function ContactChat({ isOpen, onClose }: ContactChatProps) {
  const [inputValue, setInputValue] = useState('');
  const [currentPage, setCurrentPage] = useState<'home' | 'messages' | 'chat'>('home');
  const [chatMessages, setChatMessages] = useState<Message[]>([]);
  const [isMobile, setIsMobile] = useState(false);
  const [recentMessages] = useState<RecentMessage[]>([
    {
      id: '1',
      preview: '您的问题已收到，我们会尽快给您答复...',
      sender: 'LoomRun',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      unread: true
    },
    {
      id: '2',
      preview: '感谢您使用LoomRun服务，有任何问题随时联系我们',
      sender: 'LoomRun',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: '3',
      preview: '您的网站部署已完成，请查收',
      sender: 'LoomRun',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000)
    }
  ]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 640);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 进入聊天页面时发送机器人问候消息
  useEffect(() => {
    if (currentPage === 'chat' && chatMessages.length === 0) {
      const welcomeMessage: Message = {
        id: 'welcome',
        text: '您好！我是LoomRun智能助手 🚀 当编织美学遇上暴力效率，我们正在重新定义开发生产力！请问有什么可以帮助您的吗？您可以咨询关于代码编织、一键部署等任何问题。',
        isUser: false,
        timestamp: new Date()
      };
      setChatMessages([welcomeMessage]);
    }
  }, [currentPage, chatMessages.length]);

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      // 🎯 RichTap 触觉反馈：发送消息时的专业触感
      if ('vibrate' in navigator) {
        navigator.vibrate(28); // 中等强度，"钝击"感
      }

      const newMessage: Message = {
        id: Date.now().toString(),
        text: inputValue,
        isUser: true,
        timestamp: new Date()
      };
      
      setChatMessages(prev => [...prev, newMessage]);
      setInputValue('');
      
      // 模拟机器人回复
      setTimeout(() => {
        const autoReply: Message = {
          id: (Date.now() + 1).toString(),
          text: '感谢您的咨询！我已经收到您的消息，正在为您查找相关信息。如需紧急帮助，您也可以发送邮件至 <EMAIL>，我们会在24小时内回复。',
          isUser: false,
          timestamp: new Date()
        };
        setChatMessages(prev => [...prev, autoReply]);
      }, 1000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const openChat = () => {
    setCurrentPage('chat');
  };

  // 手机端全屏样式
  useEffect(() => {
    let header: HTMLElement | null = null;
    let originalHeaderDisplay = '';
    
    if (isOpen && isMobile) {
      // 隐藏系统滚动条
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';
      
      // 查找并隐藏页面的 header 导航栏
      header = document.querySelector('header');
      originalHeaderDisplay = header?.style.display || '';
      if (header) {
        header.style.display = 'none';
      }
      
      // 隐藏地址栏
      setTimeout(() => {
        window.scrollTo(0, 1);
      }, 100);
    }
    
    return () => {
      if (isOpen && isMobile) {
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';
        
        // 恢复 header 显示
        if (header) {
          header.style.display = originalHeaderDisplay;
        }
      }
    };
  }, [isOpen, isMobile]);

  if (!isOpen) return null;

  // 首页内容
  const renderHomePage = () => (
    <div className="flex-1 p-6 space-y-6 overflow-y-auto max-sm:p-4 max-sm:pt-8 max-sm:space-y-6 max-sm:pb-8">
      {/* 问候语 */}
      <div className="space-y-3 max-sm:space-y-3">
        <h2 className="text-3xl font-medium text-foreground flex items-center gap-2 max-sm:text-4xl max-sm:font-semibold">
          您好 <span className="text-3xl max-sm:text-4xl">👋</span>
        </h2>
        <p className="text-lg text-muted-foreground max-sm:text-xl max-sm:font-medium">有什么可以帮您？</p>
      </div>

      {/* 快速操作 */}
      <div className="space-y-3 max-sm:space-y-3">
        <div className="flex items-center justify-between py-3 hover:bg-secondary rounded-lg px-3 cursor-pointer transition-colors max-sm:py-5 max-sm:px-5 max-sm:rounded-xl">
          <span className="text-foreground max-sm:text-lg max-sm:font-medium">常见问题</span>
          <svg className="w-4 h-4 text-muted-foreground max-sm:w-6 max-sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </div>
        
        <div className="flex items-center justify-between py-3 hover:bg-secondary rounded-lg px-3 cursor-pointer transition-colors max-sm:py-5 max-sm:px-5 max-sm:rounded-xl">
          <span className="text-foreground max-sm:text-lg max-sm:font-medium">投诉/反馈/建议</span>
          <svg className="w-4 h-4 text-muted-foreground max-sm:w-6 max-sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </div>
      </div>

      {/* 发送消息按钮 */}
      <button
        onClick={openChat}
        className="w-full flex items-center justify-between py-3 px-4 bg-secondary hover:bg-secondary/80 rounded-lg transition-colors max-sm:py-5 max-sm:px-5 max-sm:rounded-xl max-sm:bg-primary max-sm:hover:bg-primary/90"
      >
        <span className="text-foreground font-bold max-sm:text-lg max-sm:font-black max-sm:text-primary-foreground">向我们发送消息</span>
        <svg className="w-4 h-4 text-muted-foreground max-sm:w-6 max-sm:h-6 max-sm:text-primary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  );

  // 消息页面内容
  const renderMessagesPage = () => (
    <div className="flex-1 p-6 space-y-4 overflow-y-auto max-sm:p-4 max-sm:pt-8 max-sm:space-y-4 max-sm:pb-8">
      <h3 className="text-xl font-medium text-foreground max-sm:text-lg">最近的消息</h3>
      
      <div className="space-y-2">
        {recentMessages.map((msg) => (
          <div key={msg.id} className="flex items-center gap-3 p-3 rounded-lg hover:bg-secondary cursor-pointer transition-colors max-sm:p-4 max-sm:gap-4" onClick={openChat}>
            <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center flex-shrink-0 max-sm:w-12 max-sm:h-12">
              <span className="text-foreground text-sm font-medium max-sm:text-base">L</span>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-foreground max-sm:text-base">{msg.sender}</p>
                <p className="text-xs text-muted-foreground max-sm:text-sm">
                  {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
              <p className="text-sm text-muted-foreground truncate max-sm:text-base max-sm:mt-1">{msg.preview}</p>
            </div>
            {msg.unread && (
              <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0 max-sm:w-3 max-sm:h-3"></div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  // 聊天页面内容
  const renderChatPage = () => (
    <>
      {/* 聊天消息区域 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 max-sm:p-3 max-sm:space-y-3">
        {chatMessages.map((message) => (
          <div key={message.id} className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-[80%] rounded-2xl px-4 py-2 max-sm:max-w-[85%] max-sm:px-3 max-sm:py-3 ${
              message.isUser 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-secondary text-foreground'
            }`}>
              <p className="text-sm leading-relaxed max-sm:text-base max-sm:leading-relaxed">{message.text}</p>
              <p className={`text-xs mt-1 max-sm:text-sm max-sm:mt-2 ${
                message.isUser ? 'text-primary-foreground/70' : 'text-muted-foreground'
              }`}>
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </p>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* 聊天输入区域 */}
      <div className="border-t border-border p-4 max-sm:p-3">
        <div className="flex items-center gap-2 max-sm:gap-3">
          {/* 图片上传按钮 */}
          <button className="p-2 text-muted-foreground hover:text-foreground transition-colors max-sm:p-3">
            <Image className="w-5 h-5 max-sm:w-6 max-sm:h-6" />
          </button>
          
          {/* 表情按钮 */}
          <button className="p-2 text-muted-foreground hover:text-foreground transition-colors max-sm:p-3">
            <Smile className="w-5 h-5 max-sm:w-6 max-sm:h-6" />
          </button>
          
          {/* 输入框 */}
          <div className="flex-1 relative contact-chat">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入消息..."
              className="w-full px-4 py-2 border border-border rounded-full focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent text-sm bg-background text-foreground placeholder-muted-foreground max-sm:px-4 max-sm:py-3 max-sm:text-base"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none'
              }}
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputValue.trim()}
              className="absolute right-1 top-1 h-8 w-8 p-0 rounded-full bg-primary hover:bg-primary/90 disabled:bg-muted flex items-center justify-center transition-colors max-sm:right-1 max-sm:top-1 max-sm:h-10 max-sm:w-10"
            >
              <Send className="w-4 h-4 text-primary-foreground max-sm:w-5 max-sm:h-5" />
            </button>
          </div>
        </div>
      </div>
    </>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 z-[9999] max-sm:fixed max-sm:inset-0 max-sm:z-[99999] max-sm:bg-white max-sm:w-screen max-sm:h-screen max-sm:top-0 max-sm:left-0" style={{ position: 'fixed', bottom: '16px', right: '16px' }}>
      <div className="bg-white dark:bg-background rounded-3xl shadow-2xl border border-border w-96 h-[600px] flex flex-col max-sm:w-full max-sm:h-screen max-sm:rounded-none max-sm:border-0 max-sm:relative">
        
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-border max-sm:p-4 max-sm:pt-4 max-sm:bg-background max-sm:relative max-sm:z-10 max-sm:shadow-sm">
          <div className="flex items-center gap-3">
            {/* 返回按钮（手机端根据页面显示，桌面端仅聊天页显示） */}
            {(currentPage === 'chat' || (currentPage === 'messages' && isMobile)) && (
              <button
                onClick={() => currentPage === 'chat' ? setCurrentPage('home') : currentPage === 'messages' ? setCurrentPage('home') : null}
                className="p-2 text-muted-foreground hover:text-foreground hover:bg-secondary rounded-full transition-all duration-200 max-sm:p-2.5"
              >
                <svg className="w-5 h-5 max-sm:w-6 max-sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
            )}
            
            {/* LoomRun 完整Logo */}
            <div className="flex items-center gap-2">
              <img
                src="/favicon_io/favicon-32x32.png"
                alt="LoomRun - 织梦术师"
                width="41"
                height="41"
                className="h-8 md:h-9 lg:h-10 w-auto hover:brightness-110 transition-all duration-300 -mt-0.5"
                title="编织逻辑，运行万物 - LoomRun"
              />
              <span className="text-blue-400 font-bold text-sm md:text-base lg:text-lg hover:text-blue-300 transition-colors duration-300">
                LoomRun
              </span>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 text-muted-foreground hover:text-foreground hover:bg-secondary rounded-full transition-all duration-200 max-sm:p-2.5"
          >
            <X className="w-5 h-5 max-sm:w-6 max-sm:h-6" />
          </button>
        </div>

        {/* 页面内容 */}
        {currentPage === 'home' && renderHomePage()}
        {currentPage === 'messages' && renderMessagesPage()}
        {currentPage === 'chat' && renderChatPage()}

        {/* 底部导航（仅在首页和消息页显示） */}
        {(currentPage === 'home' || currentPage === 'messages') && (
          <div className="border-t border-border rounded-b-3xl bg-background max-sm:rounded-none max-sm:pb-0 max-sm:sticky max-sm:bottom-0">
            <div className="flex items-center w-full">
              <button
                onClick={() => setCurrentPage('home')}
                className={`flex-1 flex flex-col items-center py-4 hover:bg-secondary transition-colors cursor-pointer rounded-bl-3xl max-sm:py-6 max-sm:pb-8 max-sm:rounded-none ${
                  currentPage === 'home' ? 'text-primary' : 'text-muted-foreground'
                }`}
              >
                <div className="w-6 h-6 mb-1 max-sm:w-8 max-sm:h-8 max-sm:mb-2">
                  <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6 max-sm:w-8 max-sm:h-8">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                  </svg>
                </div>
                <span className="text-xs text-muted-foreground max-sm:text-base max-sm:font-medium">主页</span>
              </button>
              <button
                onClick={() => setCurrentPage('messages')}
                className={`flex-1 flex flex-col items-center py-4 hover:bg-secondary transition-colors cursor-pointer rounded-br-3xl max-sm:py-6 max-sm:pb-8 max-sm:rounded-none ${
                  currentPage === 'messages' ? 'text-primary' : 'text-muted-foreground'
                }`}
              >
                <div className="w-6 h-6 mb-1 max-sm:w-8 max-sm:h-8 max-sm:mb-2">
                  <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6 max-sm:w-8 max-sm:h-8">
                    <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                  </svg>
                </div>
                <span className="text-xs text-muted-foreground max-sm:text-base max-sm:font-medium">消息</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 