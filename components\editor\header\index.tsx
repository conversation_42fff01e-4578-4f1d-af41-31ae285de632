import { ReactNode, useState } from "react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { TopUserMenu } from "@/components/editor/top-user-menu";
import { ShareButton } from "@/components/editor/share-button";
import ContactChat from "@/components/contact-chat";

export function Header({
  children,
  onLogoClick,
  project,
  htmlContent,
  onMobileSidebarToggle,
}: {
  children?: ReactNode;
  onLogoClick?: () => void;
  project?: { id: number; title?: string };
  htmlContent?: string;
  onMobileSidebarToggle?: () => void;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [isChatOpen, setIsChatOpen] = useState(false);

  // 🎯 智能Logo点击处理
  const handleLogoClick = () => {
    const startTime = performance.now();
    
    // 🔍 如果已经在欢迎界面，不执行任何操作
    if (pathname === '/projects/new') {
      console.log('🏠 已在欢迎界面，忽略Logo点击');
      
      // 📊 性能日志
      if (process.env.NODE_ENV === 'development') {
        const duration = performance.now() - startTime;
        console.log(`⚡ Logo点击: 已在欢迎界面，无操作 (${duration.toFixed(1)}ms)`);
      }
      return;
    }
    
    // 🚀 在其他页面：执行跳转到欢迎界面的操作
    if (onLogoClick) {
      // 使用传入的自定义处理函数
      onLogoClick();
    } else {
      // 默认跳转到欢迎界面
      router.push('/projects/new');
    }
    
    // 📊 性能日志
    if (process.env.NODE_ENV === 'development') {
      const duration = performance.now() - startTime;
      console.log(`⚡ Logo点击: 跳转到欢迎界面 (${duration.toFixed(1)}ms)`);
    }
  };

  return (
    <>
             <header className="border-b bg-background/95 backdrop-blur-md border-border px-0.5 md:px-1 lg:px-2 grid grid-cols-3 z-50 relative flex-shrink-0 h-11 w-full">
        <div className="flex items-center justify-start gap-1 md:gap-2">
          {/* 移动端汉堡菜单按钮 - 完全透明，无边框，向上调整4% */}
          <button
            onClick={onMobileSidebarToggle}
            className="lg:hidden w-7 h-7 md:w-10 md:h-10 flex items-center justify-center hover:bg-secondary/40 active:bg-secondary/60 transition-all duration-200 group hamburger-button border-0 bg-transparent outline-none focus:outline-none"
            style={{
              WebkitTapHighlightColor: 'transparent',
              touchAction: 'manipulation',
              border: 'none',
              boxShadow: 'none',
              transform: 'translateY(-4%)'
            }}
            aria-label="打开菜单"
            title="打开侧边栏"
          >
            {/* 专业汉堡图标 - 深浅模式优化 */}
            <svg
              className="w-4 h-4 md:w-5 md:h-5 text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-200"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              strokeWidth={2.5}
            >
              <g>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 6h18"
                  className="origin-center transition-transform duration-200"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 12h18"
                  className="origin-center transition-opacity duration-200"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 18h18"
                  className="origin-center transition-transform duration-200"
                />
              </g>
            </svg>
          </button>

          {/* 专业斜杠分隔符 - 精确居中定位 */}
          <div className="lg:hidden flex items-center justify-center" style={{ width: '16px', height: '44px' }}>
            <div
              className="w-px bg-gray-400 dark:bg-gray-500"
              style={{
                height: '16px',
                transform: 'rotate(20deg)',
                position: 'relative'
              }}
            />
          </div>
          <h1 className="text-foreground text-sm font-bold flex items-center justify-start">
            <button
              onClick={handleLogoClick}
              className={`flex items-center gap-1.5 transition-all duration-200 focus:outline-none p-0 ${
                pathname === '/projects/new'
                  ? 'cursor-default opacity-90' // 在欢迎界面时的视觉状态
                  : 'hover:opacity-80 cursor-pointer' // 可点击状态
              }`}
              title={
                pathname === '/projects/new'
                  ? '当前已在欢迎界面'
                  : '返回欢迎界面'
              }
              style={{
                marginLeft: '-6px' // 🎯 优化后的斜杠分隔符，调整左边距
              }}
            >
              {/* 🎯 使用 loomrun_icon.svg 纯图标Logo - 强制放大方案 */}
              <div
                className="flex items-center justify-center overflow-hidden focus-within:ring-2 focus-within:ring-blue-500/50 rounded-lg w-10 md:w-14"
                style={{
                  height: '40px' // 🎯 保持高度不变，宽度通过className响应式控制
                }}
              >
                <Image
                  src="/loomrun_icon.svg"
                  width={1501}
                  height={1500}
                  alt="LoomRun - 织梦术师"
                  className={`transition-all duration-300 ${
                    pathname === '/projects/new' ? '' : 'hover:brightness-110'
                  }`}
                  title="编织逻辑，运行万物 - LoomRun"
                  priority
                  style={{
                    transform: 'scale(1.5)', // 🎯 调整为2.3倍缩放
                    transformOrigin: '49% 52%', // 🎯 微调缩放原点，让图标在容器中居中
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain'
                  }}
                />
              </div>
            </button>
          </h1>
        </div>
        <div className="flex items-center justify-center">
          {/* 空白中间区域 */}
        </div>
        <div className="flex items-center justify-end gap-1">
          {project && htmlContent && (
            <ShareButton 
              projectId={project.id}
              htmlContent={htmlContent}
              projectTitle={project.title}
            />
          )}
          {children}
          <TopUserMenu 
            isChatOpen={isChatOpen}
            setIsChatOpen={setIsChatOpen}
          />
        </div>
      </header>
      
      {/* 联系我们弹窗 - 完全独立于Header容器，直接相对于视口定位 */}
      <ContactChat 
        isOpen={isChatOpen} 
        onClose={() => setIsChatOpen(false)} 
      />
    </>
  );
}
