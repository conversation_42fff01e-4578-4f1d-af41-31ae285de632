"use client";
import React, { useState, useCallback, useEffect } from "react";
import { useLocalStorage } from "react-use";
// 移除motion导入以提升性能
import { ChatMessage } from "@/lib/project-manager";
import { ProjectManager } from "@/lib/project-manager";
import { AskAI } from "../editor/ask-ai";
import { ProjectSidebar } from "../project-sidebar";
import { PanelLeft, PanelLeftClose } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";
import { useRouter } from "next/navigation";
import { Header } from "../editor/header";
import { useUser } from "@/loomrunhooks/useUser";
import { useChatHistory } from "@/loomrunhooks/useChatHistory";
import { toast } from "sonner";
import { globalAIState } from "@/lib/global-ai-state";
import { CommunityPage } from "../community/community-page";
import { CommunityProjectsGrid } from "../community/community-projects-grid";
import ContactChat from "../contact-chat";
import { TempProjectManager } from "@/lib/temp-project-manager";

interface WelcomeLayoutProps {
  onStartCreating: (prompt: string, chatHistory?: ChatMessage[], html?: string) => void;
}

export function WelcomeLayout({ onStartCreating }: WelcomeLayoutProps) {
  const router = useRouter();
  const { user } = useUser();
  const chatHistoryHook = useChatHistory();
  const projectManager = ProjectManager.getInstance();
  const tempProjectManager = TempProjectManager.getInstance();
  
  // 状态管理
  const [html, setHtml] = useState('');

  // 🆕 新增：输入框状态管理
  const [inputPrompt, setInputPrompt] = useState("");
  
  // 侧边栏状态
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [openedByClick, setOpenedByClick] = useState(false);

  // 调试状态变化
  useEffect(() => {
    console.log('侧边栏状态变化:', { isSidebarOpen, openedByClick });
  }, [isSidebarOpen, openedByClick]);
     const [sidebarWidth, setSidebarWidth] = useState(269); // 侧边栏宽度 (桌面: 269px, 移动端: 100vw)
  
  // 社区项目状态
  interface CommunityProject {
    id: number;
    originalProjectId: number;
    userId: number;
    title: string;
    htmlContent: string;
    createdAt: string;
    updatedAt: string;
    author: {
      name: string;
      email: string;
    };
  }
  
  const [previewProject, setPreviewProject] = useState<CommunityProject | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isCommunityPageMode, setIsCommunityPageMode] = useState(false);
  const [isContactChatOpen, setIsContactChatOpen] = useState(false);
  
  // 🔧 添加防重复创建的状态
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [createdProjectId, setCreatedProjectId] = useState<string | null>(null);

  // 确保默认使用deepseek-chat模型
  useLocalStorage("model", "deepseek-chat");

  // 🔧 简化：不需要复杂的全局状态监听，直接在本地管理聊天历史即可

  // 🎯 修复 hydration 错误：添加客户端状态标记
  const [isClient, setIsClient] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(false);

  // 响应式宽度监听 - 移动端全屏覆盖
  useEffect(() => {
    // 🎯 标记为客户端环境
    setIsClient(true);

    const updateSidebarWidth = () => {
      const isLarge = window.innerWidth >= 1024;
      setIsLargeScreen(isLarge);
      setSidebarWidth(isLarge ? 269 : window.innerWidth); // 桌面: 269px, 移动端: 100vw
    };

    // 初始设置
    updateSidebarWidth();

    // 监听窗口尺寸变化
    window.addEventListener('resize', updateSidebarWidth);
    return () => window.removeEventListener('resize', updateSidebarWidth);
  }, []);

  // 键盘快捷键监听 - Ctrl+B 切换侧边栏
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'b') {
        event.preventDefault();
        toggleSidebar();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isSidebarOpen, openedByClick]); // 依赖于侧边栏状态

  // 🚫 移除所有页面退出提醒 - 系统自动保存，无需用户干预
  // 注释掉原有的页面退出提醒逻辑，因为：
  // 1. 已登录用户：系统自动保存项目
  // 2. 未登录用户：临时项目在localStorage中保存，页面刷新不会丢失
  // 3. 现代Web应用应该提供无障碍的用户体验，避免打断用户的正常操作

  // 🚀 全局状态管理的AI请求处理函数
  const handleAIRequest = useCallback(async (prompt: string, model?: string, images?: string[]) => {
    try {
      console.log('🚀 WelcomeLayout: 开始AI请求', {
        prompt: prompt.substring(0, 50) + '...',
        model,
        hasImages: images && images.length > 0,
        imageCount: images?.length || 0
      });
      
      // 🔧 修复：不在这里调用startGeneration，因为已经在handleFirstMessage中调用了
      // 这里只负责实际的API请求和流式处理
      
      // 模型映射
      const modelMapping = {
        "loomrun-1.2ds": "deepseek-chat",
        "loomrun-1.6db": "doubao-seed-1-6-250615"
      };
      
      const actualModel = model && modelMapping[model as keyof typeof modelMapping] 
        ? modelMapping[model as keyof typeof modelMapping] 
        : "deepseek-chat";
      
      const provider = actualModel.includes('doubao') ? 'doubao-official' : 'deepseek-official';
      
      console.log('🔄 WelcomeLayout: 模型映射结果', {
        inputModel: model,
        actualModel,
        provider,
        hasImages: images && images.length > 0
      });
      
      const response = await fetch('/api/ask-ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt,
          provider: provider,
          model: actualModel,
          images: images || []
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No reader available');
      }

      // 🎯 注册到全局状态管理
      globalAIState.setStreamReader(reader);

      let accumulatedContent = '';
      let hasDetectedComplete = false;

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          if (!hasDetectedComplete && accumulatedContent.length > 0) {
            console.log('🎯 WelcomeLayout: 流式响应完成，立即完成生成并保存');
            
            // 🔧 关键修复：立即更新AI消息状态为完成
            console.log('🔧 WelcomeLayout: 开始更新AI消息状态为完成');
            chatHistoryHook.completeAIGeneration(accumulatedContent);
            console.log('✅ WelcomeLayout: AI生成状态已更新为完成');
            
            // 🔧 关键修复：立即通知全局状态管理器AI生成完成
            console.log('🎯 WelcomeLayout: 通知全局状态管理器AI生成完成');
            globalAIState.completeGeneration(accumulatedContent);
            console.log('✅ WelcomeLayout: 全局状态已更新为完成');
            
            // 🔧 关键修复：保存到项目数据库或临时项目管理器
            try {
              const currentProjectId = projectManager.currentProjectId;
              if (currentProjectId) {
                if (currentProjectId.startsWith('temp_')) {
                  // 临时项目：保存到临时项目管理器
                  console.log('💾 WelcomeLayout: 保存AI生成内容到临时项目', currentProjectId);
                  tempProjectManager.updateProject(currentProjectId, accumulatedContent, [prompt]);
                  
                  // 更新临时项目的AI消息
                  const tempProject = tempProjectManager.getProject(currentProjectId);
                  if (tempProject && tempProject.chatHistory.length > 0) {
                    const lastMessage = tempProject.chatHistory[tempProject.chatHistory.length - 1];
                    if (lastMessage.type === 'ai' && lastMessage.isGenerating) {
                      tempProjectManager.updateChatMessage(
                        currentProjectId,
                        lastMessage.id,
                        undefined,
                        accumulatedContent,
                        false
                      );
                    }
                  }
                  console.log('💾 WelcomeLayout: 临时项目内容已更新');
                } else {
                  // 正式项目：保存到数据库
                  console.log('💾 WelcomeLayout: 保存AI生成内容到项目', currentProjectId);
                  const saveResult = await projectManager.saveConversationToProject(
                    parseInt(currentProjectId), 
                    prompt, 
                    accumulatedContent
                  );
                  console.log('💾 WelcomeLayout: 保存结果', saveResult);
                  
                  // 同时更新项目主HTML内容
                  await projectManager.updateProject(
                    parseInt(currentProjectId), 
                    accumulatedContent, 
                    [prompt]
                  );
                  console.log('💾 WelcomeLayout: 项目HTML内容已更新');
                }
              }
            } catch (error) {
              console.error('❌ WelcomeLayout: 保存失败', error);
            }
            
            // 🚀 设置最终HTML状态
            setHtml(accumulatedContent);
            
            console.log('✅ WelcomeLayout: 流式响应结束，AI生成已完成，等待跳转');
          }
          break;
        }

        const chunk = new TextDecoder().decode(value);
        accumulatedContent += chunk;

        // 🎯 更新全局状态
        globalAIState.updateContent(accumulatedContent);

        // 🔧 关键修复：先检测完成，再决定是否更新消息
        if (!hasDetectedComplete && accumulatedContent.includes('</html>')) {
          hasDetectedComplete = true;
          console.log('🎯 WelcomeLayout: 检测到完整HTML，立即完成生成并保存', {
            contentLength: accumulatedContent.length,
            hasDetectedComplete,
            timestamp: new Date().toISOString()
          });
          
          // 🔧 关键修复：检测到完整HTML后立即标记AI生成完成
          console.log('🎯 WelcomeLayout: 检测到完整HTML结构，立即完成AI生成');
          
          // 🔧 关键修复：立即更新AI消息状态为完成
          console.log('🔧 WelcomeLayout: 开始更新AI消息状态为完成');
          chatHistoryHook.completeAIGeneration(accumulatedContent);
          console.log('✅ WelcomeLayout: AI生成状态已更新为完成');
          
          // 🔧 关键修复：立即通知全局状态管理器AI生成完成
          console.log('🎯 WelcomeLayout: 通知全局状态管理器AI生成完成');
          globalAIState.completeGeneration(accumulatedContent);
          console.log('✅ WelcomeLayout: 全局状态已更新为完成');
          
          // 🔧 关键修复：保存到项目数据库或临时项目管理器
          try {
            const currentProjectId = projectManager.currentProjectId;
            if (currentProjectId) {
              if (currentProjectId.startsWith('temp_')) {
                // 临时项目：保存到临时项目管理器
                console.log('💾 WelcomeLayout: 保存AI生成内容到临时项目', currentProjectId);
                tempProjectManager.updateProject(currentProjectId, accumulatedContent, [prompt]);
                
                // 更新临时项目的AI消息
                const tempProject = tempProjectManager.getProject(currentProjectId);
                if (tempProject && tempProject.chatHistory.length > 0) {
                  const lastMessage = tempProject.chatHistory[tempProject.chatHistory.length - 1];
                  if (lastMessage.type === 'ai' && lastMessage.isGenerating) {
                    tempProjectManager.updateChatMessage(
                      currentProjectId,
                      lastMessage.id,
                      undefined,
                      accumulatedContent,
                      false
                    );
                  }
                }
                console.log('💾 WelcomeLayout: 临时项目内容已更新');
              } else {
                // 正式项目：保存到数据库
                console.log('💾 WelcomeLayout: 保存AI生成内容到项目', currentProjectId);
                const saveResult = await projectManager.saveConversationToProject(
                  parseInt(currentProjectId), 
                  prompt, 
                  accumulatedContent
                );
                console.log('💾 WelcomeLayout: 保存结果', saveResult);
                
                // 同时更新项目主HTML内容
                await projectManager.updateProject(
                  parseInt(currentProjectId), 
                  accumulatedContent, 
                  [prompt]
                );
                console.log('💾 WelcomeLayout: 项目HTML内容已更新');
              }
            }
          } catch (error) {
            console.error('❌ WelcomeLayout: 保存失败', error);
          }
          
          // 🚀 设置最终HTML状态
          setHtml(accumulatedContent);
          
          console.log('✅ WelcomeLayout: 检测到</html>，AI生成已完成，等待跳转');
          // 🔧 关键修复：检测到</html>后立即跳出循环，停止流式读取
          break;
        }
        
        // 🔧 关键修复：只有在未完成时才更新AI消息内容  
        if (!hasDetectedComplete) {
          chatHistoryHook.updateLastAIMessage(accumulatedContent);
        }
        
        // 🚀 实时更新当前页面的HTML状态（用于跳转时传递）
        setHtml(accumulatedContent);
      }

    } catch (error) {
      console.error('❌ WelcomeLayout: AI请求失败:', error);
      
      // 🎯 停止全局状态
      globalAIState.stopGeneration();
      
      chatHistoryHook.updateLastAIMessage('生成失败，请重试');
    }
  }, [chatHistoryHook, onStartCreating, setHtml, projectManager, globalAIState]);

  // 🚀 改进的第一条消息处理函数 - 防止重复创建
  const handleFirstMessage = useCallback(async (prompt: string, model?: string, images?: string[]) => {
    // 🔧 防重复检查
    if (isCreatingProject) {
      console.log('⚠️ WelcomeLayout: 项目创建中，忽略重复请求');
      return;
    }

    if (createdProjectId) {
      console.log('⚠️ WelcomeLayout: 项目已创建，忽略重复请求', { existingProjectId: createdProjectId });
      return;
    }

    setIsCreatingProject(true);
    
         try {
       console.log('🚀 WelcomeLayout: 开始处理第一条消息', {
         promptLength: prompt.length,
         hasImages: images && images.length > 0,
         model,
         isLoggedIn: !!user
       });

       if (!user) {
         console.log('⚠️ WelcomeLayout: 用户未登录，创建临时项目');
         // 未登录用户：创建临时项目并跳转到编辑模式
         const tempProjectId = tempProjectManager.createProject(prompt);
         tempProjectManager.addChatMessage(tempProjectId, 'user', prompt);
         const aiMessageId = tempProjectManager.addChatMessage(tempProjectId, 'ai');
         
         // 构建临时聊天历史
         const tempChatHistory = [
           { id: 'user_' + Date.now(), type: 'user' as const, content: prompt, timestamp: new Date(), images: images },
           { id: aiMessageId, type: 'ai' as const, content: '', timestamp: new Date(), isGenerating: true }
         ];
         
         // 🔧 关键修复：设置临时项目ID到ProjectManager
         projectManager.currentProjectId = tempProjectId;
         
         // 🔧 关键修复：立即启动全局AI状态管理
         globalAIState.startGeneration(tempProjectId, prompt, tempChatHistory);
         
         onStartCreating(prompt, tempChatHistory, '');
         
         // 后台处理AI生成
         setTimeout(() => {
           handleAIRequest(prompt, model, images);
         }, 100);
         
         return;
       }

       const updatedChatHistory = [...chatHistoryHook.chatHistory];
       
       // 🔧 添加用户消息到聊天历史
       const userMessage = chatHistoryHook.addUserMessage(prompt, images);
       updatedChatHistory.push(userMessage);

       // 🔧 添加AI消息到聊天历史（准备接收生成内容）
       const aiMessage = chatHistoryHook.addAIMessage();
       updatedChatHistory.push({
         id: aiMessage.id,
         type: 'ai',
         content: '',
         timestamp: new Date(),
         isGenerating: true
       });

       // 🚀 立即跳转到编辑模式
       console.log('🎯 WelcomeLayout: 立即跳转到编辑模式');
       onStartCreating(prompt, updatedChatHistory, '');

       // 🚀 后台处理：项目创建和AI生成完全在后台进行
      setTimeout(async () => {
        try {
          console.log('🔧 WelcomeLayout: 后台开始创建项目');
          
          // 🎯 启动全局AI状态管理（使用临时项目ID）
          const tempProjectId = `temp-${Date.now()}`;
          globalAIState.startGeneration(tempProjectId, prompt, updatedChatHistory);
          
                     // 🔧 先开始AI请求（不阻塞）
           handleAIRequest(prompt, model, images);

          // 🚀 后台创建项目
          const projectIdStr = await projectManager.createProjectFromFirstMessage(prompt);
          
          if (projectIdStr) {
            console.log('✅ WelcomeLayout: 项目创建成功，更新全局状态', { projectId: projectIdStr });
            
            // 🔧 记录已创建的项目ID
            setCreatedProjectId(projectIdStr);
            
            // 🔧 更新全局状态中的项目ID
            const currentState = globalAIState.getState();
            if (currentState.isGenerating) {
              globalAIState.startGeneration(projectIdStr, prompt, updatedChatHistory);
            }
            
            // 🚀 保存用户消息到数据库
            try {
              const response = await fetch(`/api/me/projects/${projectIdStr}/chat`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  userPrompt: prompt,
                  aiHtmlContent: '',
                  saveUserMessageOnly: true,
                  images: images // 传递图片信息
                }),
              });
              
              if (response.ok) {
                console.log('✅ WelcomeLayout: 用户消息已保存到数据库');
              }
            } catch (saveError) {
              console.error('❌ WelcomeLayout: 保存用户消息失败', saveError);
            }

            // 🔧 静默更新URL到具体项目（不重新加载页面）
            console.log('🎯 WelcomeLayout: 静默更新URL到具体项目');
            window.history.replaceState(null, '', `/projects/${projectIdStr}`);
            
            // 🚀 关键修复：广播项目创建事件，通知项目栏更新
            console.log('📢 WelcomeLayout: 广播项目创建事件', { projectId: projectIdStr, title: prompt });
            try {
              const projectData = {
                projectId: projectIdStr,
                title: prompt,
                html_content: '<div>正在生成...</div>',
                timestamp: Date.now()
              };
              
              // 🔧 方式1：BroadcastChannel（现代浏览器）
              if ('BroadcastChannel' in window) {
                const channel = new BroadcastChannel('project-updates');
                channel.postMessage({
                  type: 'project-created',
                  ...projectData
                });
                channel.close();
                console.log('✅ WelcomeLayout: BroadcastChannel 事件已发送');
              }
              
              // 🔧 方式2：localStorage事件（兼容性更好）
              localStorage.setItem('project-created-event', JSON.stringify(projectData));
              console.log('✅ WelcomeLayout: localStorage 事件已设置');
              
              // 🔧 方式3：自定义事件（同页面通信）
              window.dispatchEvent(new CustomEvent('refreshProjectList', {
                detail: projectData
              }));
              console.log('✅ WelcomeLayout: 自定义事件已发送');
              
              // 🔧 方式4：定时检查标记（兜底方案）
              localStorage.setItem('project-list-refresh-trigger', projectIdStr);
              console.log('✅ WelcomeLayout: 刷新触发标记已设置');
              
            } catch (broadcastError) {
              console.error('❌ WelcomeLayout: 广播项目创建事件失败', broadcastError);
            }
            
          } else {
            console.error('❌ WelcomeLayout: 项目创建失败');
            toast.error('项目创建失败，但您可以继续使用');
          }
          
        } catch (error) {
          console.error('❌ WelcomeLayout: 后台处理失败', error);
          toast.error('后台处理出现错误，但不影响当前使用');
                 } finally {
           setIsCreatingProject(false);
           // 🔧 不要立即清理createdProjectId，让它保持一段时间防止重复
           setTimeout(() => {
             setCreatedProjectId(null);
           }, 30000); // 30秒后清理
         }
      }, 0);
      
    } catch (error) {
      console.error('❌ WelcomeLayout: 项目创建失败', error);
      toast.error('项目创建失败，请重试');
    }
  }, [user, chatHistoryHook, onStartCreating, handleAIRequest, projectManager, globalAIState, isCreatingProject, createdProjectId]);

  // �� 简化流程：处理新提示并直接跳转到编辑器
  const handleNewPrompt = useCallback((promptText: string, model?: string, images?: string[]) => {
    console.log('🎯 WelcomeLayout: 处理新提示，直接跳转到编辑器:', promptText);
    
    // 🎯 隐藏功能：loom.run() 
    if (promptText.toLowerCase().includes('loom.run()')) {
      console.log('✨ 隐藏功能激活：loom.run()');
      const debugInfo = {
        projectManager: projectManager.currentProjectId,
        chatHistory: chatHistoryHook.chatHistory.length,
        htmlLength: html.length,
        user: user?.id
      };
      console.log('🔧 调试信息:', debugInfo);
      alert('✨ LoomRun 隐藏功能已激活！\n\n🔧 调试信息：\n- 项目ID: ' + (projectManager.currentProjectId || '无') + '\n- 聊天记录: ' + chatHistoryHook.chatHistory.length + ' 条\n- HTML长度: ' + html.length + ' 字符\n- 用户ID: ' + (user?.id || '未登录'));
      return;
    }
    
    // 🚀 使用requestAnimationFrame确保在下一帧立即执行跳转
    requestAnimationFrame(() => {
      handleFirstMessage(promptText, model, images);
    });
  }, [handleFirstMessage, projectManager, chatHistoryHook.chatHistory, html, user]);

  // 🎯 社区项目加载函数已移至CommunityProjectsGrid组件中

  // 🎯 处理预览项目
  const handlePreviewProject = useCallback((project: CommunityProject) => {
    setPreviewProject(project);
    setIsPreviewMode(true);
  }, []);

  // 🎯 处理打开社区项目
  const handleOpenCommunityProject = useCallback(async (project: CommunityProject) => {
    try {
      // 🎯 使用专门的社区项目创建方法
      const projectIdStr = await projectManager.createProjectFromCommunityProject(project.title, project.htmlContent);

      if (projectIdStr) {
        // 🎯 跳转到编辑器，对话历史已在后端自动创建
        router.push(`/projects/${projectIdStr}`);
        toast.success(`已导入社区项目: ${project.title}`);
      }
    } catch (error) {
      console.error('打开社区项目失败:', error);
      toast.error('打开项目失败，请重试');
    }
  }, [projectManager, router]);

  // 🎯 社区项目加载已移至CommunityProjectsGrid组件中



  // 创建新粒子

  // 侧边栏鼠标事件处理
  const handleMouseEnter = () => {
    if (!openedByClick) {
      setIsSidebarOpen(true);
    }
  };



  const toggleSidebar = () => {
    if (isSidebarOpen && openedByClick) {
      // 如果是点击打开的，点击关闭
      setIsSidebarOpen(false);
      setOpenedByClick(false);
    } else if (!isSidebarOpen) {
      // 如果是关闭状态，点击打开
      setIsSidebarOpen(true);
      setOpenedByClick(true);
    } else if (isSidebarOpen && !openedByClick) {
      // 如果是悬停打开的，点击后保持打开状态但标记为点击打开
      setOpenedByClick(true);
    }
  };

  // 🎯 修复收起逻辑 - 移动端关闭按钮应该能正常关闭
  const handleSmoothClose = useCallback(() => {
    console.log('handleSmoothClose 被调用', {
      isSidebarOpen,
      openedByClick,
      '即将设置': { isSidebarOpen: false, openedByClick: false }
    });
    setIsSidebarOpen(false);
    setOpenedByClick(false);
    console.log('状态已更新');
  }, [isSidebarOpen, openedByClick]);

  return (
    <TooltipProvider>
      <div className="bg-background w-full min-h-screen welcome-layout">
        {/* 使用编辑器的真实顶部栏 */}
        <div className="fixed top-0 left-0 right-0 z-40 w-full">
          <Header
            onLogoClick={() => {
              // 点击Logo时的行为，可以跳转到首页或其他操作
              router.push('/');
            }}
            onMobileSidebarToggle={() => {
              // 移动端侧边栏切换
              if (isClient && !isLargeScreen) {
                toggleSidebar();
              }
            }}
          />
        </div>

        {/* 预览模式：显示全屏预览 */}
        {isPreviewMode && previewProject && (
          <div className="fixed left-0 right-0 bottom-0 bg-white z-20" style={{ top: '44px' }}>
            {/* 预览工具栏 - 从系统顶部栏下方开始 */}
            <div className="h-12 bg-card border-b border-border flex items-center justify-between px-4">
                              <h2 className="text-lg font-semibold text-foreground">
                  {previewProject.title}
                </h2>
                <button
                  onClick={() => setIsPreviewMode(false)}
                  className="bg-secondary border border-border text-foreground hover:bg-secondary/80 px-3 py-1 rounded text-sm flex items-center gap-2"
              >
                <span>×</span>
                关闭预览
              </button>
            </div>

            {/* 预览内容 - 占据剩余空间 */}
            <div className="h-[calc(100%-3rem)] bg-white">
              <iframe
                srcDoc={previewProject.htmlContent}
                className="w-full h-full border-0"
                title={`预览: ${previewProject.title}`}
              />
            </div>
          </div>
        )}

        {/* 🎯 社区页面模式：专业全屏布局，避开系统顶部栏 */}
        {isCommunityPageMode && (
          <div className="fixed left-0 right-0 bottom-0 z-30 bg-background" style={{ top: '44px' }}>
            <CommunityPage
              onClose={() => setIsCommunityPageMode(false)}
              onOpenProject={handleOpenCommunityProject}
              onPreviewProject={handlePreviewProject}
              avoidSystemHeader={false}
            />
          </div>
        )}

        {/* 桌面端侧边栏切换按钮 - 只在桌面端显示 */}
        {!isPreviewMode && !isCommunityPageMode && isClient && isLargeScreen && (
          // 🎯 关键修复：鼠标悬停展开时完全隐藏按钮，只有关闭状态或点击打开时才显示
          (!isSidebarOpen || openedByClick) && (
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={toggleSidebar}
                  className="fixed h-10 w-10 p-0 text-muted-foreground hover:text-foreground transition-all duration-300 z-50 hover:bg-accent/50 rounded-md"
                  style={{
                    top: 'calc(44px + 4px)',
                    left: (isSidebarOpen && openedByClick) ? `${sidebarWidth + 8}px` : '8px'
                  }}
                >
                  {(isSidebarOpen && openedByClick) ? (
                    <PanelLeftClose className="h-5 w-5" />
                  ) : (
                    <PanelLeft className="h-5 w-5" />
                  )}
                </button>
              </TooltipTrigger>
              <TooltipContent side="right" className="bg-popover text-popover-foreground border-border">
                <p className="text-sm">
                  {(isSidebarOpen && openedByClick) ? '关闭' : '打开'}项目栏 <kbd className="ml-1 px-1.5 py-0.5 text-xs bg-neutral-700 rounded">Ctrl+B</kbd>
                </p>
              </TooltipContent>
            </Tooltip>
          )
        )}

        {/* 主内容容器 - 移除顶部距离，由内部滚动容器处理 */}
        <div className="flex">
          {/* 侧边栏触发区域 - 鼠标悬停边缘打开 */}
          <div
            className="fixed left-0 w-4 z-40"
            style={{
              top: '44px', // 🎯 精准修正：44px 系统真实顶部栏高度
              height: 'calc(100vh - 44px)' // 🎯 精准修正：减去系统真实顶部栏高度44px
            }}
            onMouseEnter={handleMouseEnter}
          />

          {/* 项目侧边栏 - 直接使用固定定位 */}
          <ProjectSidebar
            isOpen={isSidebarOpen}
            openedByClick={openedByClick}
            onClose={() => {
              // 🎯 使用简化收起逻辑
              handleSmoothClose();
            }}
            onProjectSelect={(project) => {
              // 当选择项目时，直接跳转到项目页面
              router.push(`/projects/${project.id}`);
            }}
            onNewProject={() => {
              // 新建项目时关闭侧边栏并触发新建项目流程
              setIsSidebarOpen(false);
              setOpenedByClick(false);
              // 触发新建项目，传递空的提示和HTML内容
              onStartCreating('开始新项目', [], '');
            }}
            onOpenCommunityPage={() => {
              // 打开社区页面
              setIsCommunityPageMode(true);
            }}
            onOpenFavoritesPage={() => {
              // 打开收藏页面
              router.push('/favorites');
            }}
          />

          {/* 主要内容区域 - 添加滚动功能 */}
          {!isCommunityPageMode && (
            <div 
              className="flex-1 transition-all duration-300 overflow-y-auto scroll-optimized"
              style={{
                // 🎯 精准边距：与侧边栏宽度保持一致
                marginLeft: (isSidebarOpen && openedByClick) ? `${sidebarWidth}px` : '0px',
                // 🎯 精准修正：设置高度为视口高度减去系统真实顶部栏高度
                height: 'calc(100vh - 44px)', // 🎯 精准修正：44px 系统真实顶部栏高度
                marginTop: '44px' // 🎯 精准修正：确保内容从系统真实顶部栏下方开始
              }}
            >
              {/* 欢迎界面 - 可滚动内容 */}
              <div className="relative z-10 flex flex-col pb-20">
                {/* 静态科技感背景 */}
                <div className="absolute inset-0 -z-10">
                  {/* 深色模式专用网格背景 */}
                  <div 
                    className="absolute inset-0 dark:block hidden"
                    style={{
                      backgroundImage: `
                        linear-gradient(rgba(6, 182, 212, 0.02) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(6, 182, 212, 0.02) 1px, transparent 1px),
                        linear-gradient(rgba(59, 130, 246, 0.015) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(59, 130, 246, 0.015) 1px, transparent 1px)
                      `,
                      backgroundSize: "80px 80px, 80px 80px, 40px 40px, 40px 40px"
                    }}
                  />
                  
                  {/* 浅色模式专用网格背景 - 降低透明度 */}
                  <div 
                    className="absolute inset-0 block dark:hidden"
                    style={{
                      backgroundImage: `
                        linear-gradient(rgba(59, 130, 246, 0.04) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(59, 130, 246, 0.04) 1px, transparent 1px),
                        linear-gradient(rgba(16, 185, 129, 0.025) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(16, 185, 129, 0.025) 1px, transparent 1px)
                      `,
                      backgroundSize: "80px 80px, 80px 80px, 40px 40px, 40px 40px"
                    }}
                  />
                  
                  {/* 浅色模式微妙点阵 - 进一步降低透明度 */}
                  <div 
                    className="absolute inset-0 block dark:hidden opacity-10"
                    style={{
                      backgroundImage: `
                        radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.08) 1px, transparent 0)
                      `,
                      backgroundSize: "40px 40px"
                    }}
                  />
                </div>

                                    {/* 主要内容 - 响应式居中布局 */}
                <div
                  className="flex flex-col items-center justify-start px-4 pt-8 sm:pt-12 md:pt-16 relative z-20 min-h-[25vh] sm:min-h-[55vh] md:min-h-[60vh]"
                  style={{
                    // 🎯 整体向下移动15%
                    transform: 'translateY(15vh)',
                    transformOrigin: 'center'
                  }}
                >
                  {/* 标题和输入框组合 - 紧凑布局 */}
                  {/* 标题区域 - 独立空间 */}
                  <div className="w-full flex justify-center px-4 mb-8">
                    <h1
                      className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl"
                      style={{
                        fontFamily: '"SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, sans-serif',
                        fontWeight: 900,
                        letterSpacing: '-0.06em',
                        lineHeight: 1.1,
                        textShadow: '0 0 30px rgba(255, 255, 255, 0.1)',
                        transform: 'scale(1.0)',
                        transformOrigin: 'center',
                        whiteSpace: 'nowrap',
                        textAlign: 'center'
                      }}
                    >
                      <span className="text-black dark:text-white">The </span>
                      <span className="text-blue-500">LoomRun</span>
                      <span className="text-black dark:text-white"> for </span>
                      <span className="text-green-500">Site Design</span>
                    </h1>
                  </div>

                  {/* 输入框区域 - 响应式独立空间 */}
                  <div className="max-w-xl sm:max-w-2xl mx-auto w-full px-4 sm:px-2 md:px-0 mt-6 sm:mt-4 md:mt-0">
                    {/* AI输入框 */}
                    <div className="mb-6 sm:mb-8">
                    <AskAI 
                      isWelcomeMode={true}
                      inputPrompt={inputPrompt}
                      setInputPrompt={setInputPrompt}
                      onAskAI={(prompt: string, model: string, images?: string[]) => {
                        handleNewPrompt(prompt, model, images);
                      }}
                    />
                    </div>
                  </div>

                  {/* 快捷输入按钮 - 响应式布局 */}
                  <div className="mb-0 max-w-3xl mx-auto w-full px-4 sm:px-2 md:px-0">
                    <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-3">
                      {[
                        {
                          text: "5858写在页面中间即可",
                          icon: "🎨"
                        },
                        {
                          text: "制作商业PPT演示",
                          icon: "📊"
                        },
                        {
                          text: "俄罗斯方块游戏",
                          icon: "🎮"
                        },
                        {
                          text: "企业官网首页",
                          icon: "🏢"
                        }
                      ].map((item, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            // 直接更新React状态，填充到输入框
                            setInputPrompt(item.text);
                          }}
                          className="
                            group relative px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border
                            bg-neutral-800/50 dark:bg-neutral-800/50 bg-white/80 border-neutral-700 dark:border-neutral-700 border-gray-300
                            hover:bg-neutral-700/70 dark:hover:bg-neutral-700/70 hover:bg-gray-100/90 hover:border-neutral-600 dark:hover:border-neutral-600 hover:border-gray-400
                            transition-all duration-200 flex items-center gap-1.5 sm:gap-2
                            text-gray-900 dark:text-neutral-300 hover:text-gray-950 dark:hover:text-white text-xs font-medium
                            backdrop-blur-sm min-h-[36px] sm:min-h-[40px]
                          "
                        >
                          {/* 图标 */}
                          <span className="text-sm sm:text-base leading-none flex-shrink-0">
                            {item.icon}
                          </span>

                          {/* 文本 */}
                          <span className="whitespace-nowrap text-xs sm:text-xs truncate max-w-[120px] sm:max-w-none">
                            {item.text}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 社区展示区域 - 与输入框同层级，移动端优化位置 */}
                  <div
                    className="mt-8 sm:mt-20 max-w-7xl mx-auto w-full px-2 sm:px-1 md:px-2 community-section-mobile-optimized"
                  >
                    {/* 🚀 使用新的分页社区项目网格组件 */}
                    <CommunityProjectsGrid
                      onPreview={handlePreviewProject}
                      onOpen={handleOpenCommunityProject}
                      onOpenCommunityPage={() => setIsCommunityPageMode(true)}
                      onOpenContactModal={() => setIsContactChatOpen(true)}
                    />
                  </div>
                </div>


              </div>
            </div>
          )}
        </div>
      </div>

      {/* 联系我们聊天 */}
      <ContactChat
        isOpen={isContactChatOpen}
        onClose={() => setIsContactChatOpen(false)}
      />

    </TooltipProvider>
  );
}
