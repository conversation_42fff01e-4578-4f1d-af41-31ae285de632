import mysql from 'mysql2/promise';

// 🚀 企业级数据库配置 - 性能优化
const DB_CONFIG = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+08:00',
  // 🔥 连接池优化配置
  acquireTimeout: 10000,           // 10秒获取连接超时
  timeout: 30000,                  // 30秒查询超时
  reconnect: true,                 // 自动重连
  supportBigNumbers: true,         // 支持大数字
  bigNumberStrings: false,         // 数字作为数字返回而非字符串
  dateStrings: false,              // 日期作为Date对象返回
  debug: false,                    // 生产环境关闭调试
  multipleStatements: false,       // 安全：禁止多语句
  nestTables: false,               // 不嵌套表名
  typeCast: true,                  // 自动类型转换
};

let pool: mysql.Pool | null = null;

export const getPool = () => {
  if (!pool) {
    pool = mysql.createPool({
      ...DB_CONFIG,
      // 🚀 企业级连接池配置
      waitForConnections: true,      // 等待可用连接
      connectionLimit: 20,           // 增加连接池大小到20（支持更高并发）
      maxIdle: 8,                    // 最大空闲连接数
      idleTimeout: 300000,           // 5分钟空闲超时
      queueLimit: 50,                // 增加队列限制
      enableKeepAlive: true,         // 启用Keep-Alive
      keepAliveInitialDelay: 10000,  // Keep-Alive初始延迟
    });
    
    // 🔧 连接池事件监听 - 便于监控和调试
    pool.on('connection', (connection) => {
      console.log('🔗 MySQL连接已建立', { connectionId: connection.threadId });
    });
    
    pool.on('enqueue', () => {
      console.log('🔄 连接请求已排队');
    });
  }
  return pool;
};

export const executeQuery = async (query: string, params: unknown[] = []) => {
  const connection = getPool();
  let retries = 3;
  
  while (retries > 0) {
    try {
      const [results] = await connection.execute(query, params);
      return results;
    } catch (error: unknown) {
      console.error('Database query error:', error);
      
      if (error && typeof error === 'object' && 'code' in error) {
        const errorCode = (error as { code: string }).code;
        if (errorCode === 'ECONNRESET' || errorCode === 'PROTOCOL_CONNECTION_LOST') {
          retries--;
          if (retries > 0) {
            console.log(`Connection error, retrying... (${retries} attempts left)`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            continue;
          }
        }
      }
      
      throw error;
    }
  }
};

export const initDatabase = async () => {
  const connection = getPool();
  
  // 创建用户表
  const createUsersTable = `
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      phone VARCHAR(20) UNIQUE,
      wechat_openid VARCHAR(100) UNIQUE,
      nickname VARCHAR(100),
      avatar_url VARCHAR(500),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      last_login TIMESTAMP NULL,
      is_active BOOLEAN DEFAULT TRUE,
      INDEX idx_phone (phone),
      INDEX idx_wechat (wechat_openid),
      INDEX idx_created (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  // 创建项目表
  const createProjectsTable = `
    CREATE TABLE IF NOT EXISTS projects (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NOT NULL,
      title VARCHAR(200) NOT NULL,
      html_content LONGTEXT NOT NULL,
      prompts JSON,
      is_deployed BOOLEAN DEFAULT FALSE,
      deploy_url VARCHAR(500),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      INDEX idx_user_created (user_id, created_at),
      INDEX idx_created (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  // 创建项目版本表
  const createProjectVersionsTable = `
    CREATE TABLE IF NOT EXISTS project_versions (
      id INT AUTO_INCREMENT PRIMARY KEY,
      project_id INT NOT NULL,
      user_id INT NOT NULL,
      version_number INT NOT NULL,
      title VARCHAR(200),
      prompt TEXT,
      html_content LONGTEXT NOT NULL,
      metadata JSON,
      parent_version_id INT NULL,
      is_active BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (parent_version_id) REFERENCES project_versions(id) ON DELETE SET NULL,
      UNIQUE KEY unique_project_version (project_id, version_number),
      INDEX idx_project_version (project_id, version_number),
      INDEX idx_project_created (project_id, created_at),
      INDEX idx_active (project_id, is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  // 创建聊天历史表
  const createChatHistoryTable = `
    CREATE TABLE IF NOT EXISTS chat_history (
      id INT AUTO_INCREMENT PRIMARY KEY,
      project_id INT NOT NULL,
      user_id INT NOT NULL,
      message_type ENUM('user', 'ai') NOT NULL,
      content TEXT,
      html_content LONGTEXT NULL,
      version_id INT NULL,
      metadata JSON,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (version_id) REFERENCES project_versions(id) ON DELETE SET NULL,
      INDEX idx_project_created (project_id, created_at),
      INDEX idx_user_project (user_id, project_id),
      INDEX idx_version (version_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  // 创建验证码表
  const createVerificationTable = `
    CREATE TABLE IF NOT EXISTS phone_verifications (
      id INT AUTO_INCREMENT PRIMARY KEY,
      phone VARCHAR(20) NOT NULL,
      code VARCHAR(6) NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      expires_at TIMESTAMP NOT NULL,
      is_used BOOLEAN DEFAULT FALSE,
      INDEX idx_phone_code (phone, code),
      INDEX idx_expires (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  // 创建社区项目表
  const createCommunityProjectsTable = `
    CREATE TABLE IF NOT EXISTS community_projects (
      id INT AUTO_INCREMENT PRIMARY KEY,
      original_project_id INT NOT NULL,
      user_id INT NOT NULL,
      title VARCHAR(255) NOT NULL,
      html_content LONGTEXT NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      
      -- 外键约束
      FOREIGN KEY (original_project_id) REFERENCES projects(id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      
      -- 索引
      INDEX idx_user_id (user_id),
      INDEX idx_original_project_id (original_project_id),
      INDEX idx_created_at (created_at),
      INDEX idx_updated_at (updated_at),
      INDEX idx_title (title),
      
      -- 唯一约束：每个项目只能共享一次
      UNIQUE KEY unique_project_share (original_project_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  try {
    await connection.execute(createUsersTable);
    await connection.execute(createProjectsTable);
    await connection.execute(createProjectVersionsTable);
    await connection.execute(createChatHistoryTable);
    await connection.execute(createVerificationTable);
    await connection.execute(createCommunityProjectsTable);
    console.log('Database tables initialized successfully (including community_projects)');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
};

export default { getPool, executeQuery, initDatabase }; 