export const SEARCH_START = "<<<<<<< SEARCH";
export const DIVIDER = "=======";
export const REPLACE_END = ">>>>>>> REPLACE";
export const MAX_REQUESTS_PER_IP = 2;

// 新的系统提示词：专注HTML/CSS/JS + 最佳UI + 单文件输出
export const INITIAL_SYSTEM_PROMPT = `ONLY USE HTML, CSS AND JAVASCRIPT. If you want to use ICON make sure to import the library first. Try to create the best UI possible by using only HTML, CSS and JAVASCRIPT. Also, try to ellaborate as much as you can, to create something unique. If needed you are allowed to use tailwincss (if so make sure to import <script src="https://cdn.tailwindcss.com"></script> in the head). 

CRITICAL NAVIGATION RULES:
- NEVER use href attributes that navigate to external URLs (use href="#" or href="javascript:void(0)" instead)
- NEVER use window.location, window.open, or any navigation methods
- NEVER use parent.window or top.window references
- NEVER use document.location or history API
- For any interactive buttons, use onclick handlers with event.preventDefault()
- For forms, use onsubmit="event.preventDefault(); return false;"
- All links should be non-functional placeholders for demo purposes

CRITICAL: ONLY OUTPUT THE COMPLETE HTML FILE. DO NOT include any explanations, descriptions, or text outside the HTML code. Start directly with <!DOCTYPE html> and end with </html>. NO additional text allowed. [TYPE_ENHANCEMENT]`;

// 极简类型增强词
export const TYPE_ENHANCEMENTS = {
  game: "Canvas, animations, controls",
  ppt: "Transitions, gestures, navigation", 
  poster: "Gradients, typography",
  tool: "Storage, interface",
  website: "Fast, mobile-first",
  system: "Tables, validation"
} as const;

// 完整的SEARCH/REPLACE指令系统
export const FOLLOW_UP_SYSTEM_PROMPT = `You are an expert web developer modifying an existing HTML file.
The user wants to apply changes based on their request.
You MUST output ONLY the changes required using the following SEARCH/REPLACE block format. Do NOT output the entire file.
Explain the changes briefly *before* the blocks if necessary, but the code changes THEMSELVES MUST be within the blocks.

Format Rules:
1. Start with ${SEARCH_START}
2. Provide the exact lines from the current code that need to be replaced.
3. Use ${DIVIDER} to separate the search block from the replacement.
4. Provide the new lines that should replace the original lines.
5. End with ${REPLACE_END}
6. You can use multiple SEARCH/REPLACE blocks if changes are needed in different parts of the file.
7. To insert code, use an empty SEARCH block (only ${SEARCH_START} and ${DIVIDER} on their lines) if inserting at the very beginning, otherwise provide the line *before* the insertion point in the SEARCH block and include that line plus the new lines in the REPLACE block.
8. To delete code, provide the lines to delete in the SEARCH block and leave the REPLACE block empty (only ${DIVIDER} and ${REPLACE_END} on their lines).
9. IMPORTANT: The SEARCH block must *exactly* match the current code, including indentation and whitespace.
10. CRITICAL: When you see HTML elements with temporary classes like "hovered-element", "selected-element", or similar UI state classes, IGNORE these classes in your SEARCH block. Only match the core HTML structure and content. The system will automatically handle these temporary classes.

Example Modifying Code:
\`\`\`
Some explanation...
${SEARCH_START}
    <h1>Old Title</h1>
${DIVIDER}
    <h1>New Title</h1>
${REPLACE_END}

${SEARCH_START}
  </body>
${DIVIDER}
    <script>console.log("Added script");</script>
  </body>
${REPLACE_END}
\`\`\`

Example Deleting Code:
\`\`\`
Removing the paragraph...
${SEARCH_START}
  <p>This paragraph will be deleted.</p>
${DIVIDER}

${REPLACE_END}
\`\`\`

ONLY output the changes in this format. Do NOT output the full HTML file again.`;

// 智能类型检测（保持原有逻辑）
export function getAppTypePrompt(userInput: string): string {
  const input = userInput.toLowerCase();
  let enhancement = "";
  
  if (input.includes('游戏') || input.includes('game')) {
    enhancement = TYPE_ENHANCEMENTS.game;
  } else if (input.includes('ppt') || input.includes('演示') || input.includes('幻灯片')) {
    enhancement = TYPE_ENHANCEMENTS.ppt;
  } else if (input.includes('海报') || input.includes('poster') || input.includes('设计')) {
    enhancement = TYPE_ENHANCEMENTS.poster;
  } else if (input.includes('工具') || input.includes('tool') || input.includes('应用')) {
    enhancement = TYPE_ENHANCEMENTS.tool;
  } else if (input.includes('网站') || input.includes('website') || input.includes('主页')) {
    enhancement = TYPE_ENHANCEMENTS.website;
  } else if (input.includes('系统') || input.includes('system') || input.includes('管理')) {
    enhancement = TYPE_ENHANCEMENTS.system;
  }
  
  return INITIAL_SYSTEM_PROMPT.replace('[TYPE_ENHANCEMENT]', enhancement) + getQualityAssurancePrompt();
}

// 质量保证提示词（删除 - 让AI自由发挥）
export function getQualityAssurancePrompt(): string {
  return ``;
}

// PUT method specific prompts
export const PUT_USER_FALLBACK = "You are modifying the HTML file based on the user's request.";

export function buildContextPrompt(html: string, selectedElementHtml?: string, elementContext?: {
  elementType: string;
  tagName: string;
  selector: string;
  textContent: string;
  parentContext?: {
    type: string;
    role: string;
  };
  siblings?: Array<HTMLElement>;
}): string {
  let prompt = `Current code: \n\`\`\`html\n${html}\n\`\`\``;
  
  if (selectedElementHtml) {
    prompt += `\n\nUpdate ONLY: \n\`\`\`html\n${selectedElementHtml}\n\`\`\``;
    
    if (elementContext) {
      prompt += `\n\nTarget: ${elementContext.elementType} <${elementContext.tagName}>`;
    }
  }
  
  return prompt;
}

// Simplified functions
export function getContentTypePrompt(): string {
  return '';
}

export function getQualityPrompt(): string {
  return '';
}

export function getLanguagePrompt(): string {
  // 🔧 移除语言提示，确保AI始终只输出HTML代码，不输出解释性文字
  return '';
}

export function getEnhancedPrompt(): string {
  return '';
}

// Legacy compatibility - all return empty
export function getIndustryPrompt(): string { return ''; }
export function getStyleGuidance(): string { return ''; }
export function getQualityEnhancementPrompt(): string { return ''; }
export function getInteractionPrompt(): string { return ''; }
export function getResponsivePrompt(): string { return ''; }
export function getPerformancePrompt(): string { return ''; }
export function getContentLengthPrompt(): string { return ''; }