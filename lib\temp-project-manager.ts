import { toast } from "sonner";

export interface TempProject {
  id: string;
  title: string;
  html_content: string;
  prompts: string[];
  created_at: string;
  updated_at: string;
  chatHistory: Array<{
    id: string;
    type: 'user' | 'ai';
    content?: string;
    html_content?: string;
    timestamp: string;
    isGenerating?: boolean;
  }>;
}

export class TempProjectManager {
  private static instance: TempProjectManager;
  private projects: Map<string, TempProject> = new Map();
  private currentProjectId: string | null = null;
  
  // 智能提醒控制
  private lastReminderTime: number = 0;
  private reminderCount: number = 0;
  private sessionStartTime: number = Date.now();
  private hasUserInteracted: boolean = false;

  private constructor() {
    // 从localStorage恢复数据
    this.loadFromStorage();
    
    // 监听页面卸载事件，保存数据
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.saveToStorage();
      });
      
      // 监听用户交互，标记用户活跃
      const markUserActive = () => {
        this.hasUserInteracted = true;
      };
      
      document.addEventListener('click', markUserActive, { once: true });
      document.addEventListener('keydown', markUserActive, { once: true });
    }
  }

  static getInstance(): TempProjectManager {
    if (!TempProjectManager.instance) {
      TempProjectManager.instance = new TempProjectManager();
    }
    return TempProjectManager.instance;
  }

  // 从localStorage加载数据
  private loadFromStorage(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const stored = localStorage.getItem('loomrun_temp_projects');
      if (stored) {
        const data = JSON.parse(stored);
        this.projects = new Map(data.projects || []);
        this.currentProjectId = data.currentProjectId || null;
        this.lastReminderTime = data.lastReminderTime || 0;
        this.reminderCount = data.reminderCount || 0;
        console.log('📦 TempProjectManager: 从localStorage恢复数据', {
          projectCount: this.projects.size,
          currentProjectId: this.currentProjectId
        });
      }
    } catch (error) {
      console.error('❌ TempProjectManager: 加载数据失败', error);
    }
  }

  // 保存数据到localStorage
  private saveToStorage(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const data = {
        projects: Array.from(this.projects.entries()),
        currentProjectId: this.currentProjectId,
        lastReminderTime: this.lastReminderTime,
        reminderCount: this.reminderCount
      };
      localStorage.setItem('loomrun_temp_projects', JSON.stringify(data));
      console.log('💾 TempProjectManager: 数据已保存到localStorage');
    } catch (error) {
      console.error('❌ TempProjectManager: 保存数据失败', error);
    }
  }

  // 创建新的临时项目
  createProject(firstMessage: string): string {
    const projectId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();
    
    const project: TempProject = {
      id: projectId,
      title: this.generateTitle(firstMessage),
      html_content: '',
      prompts: [firstMessage],
      created_at: now,
      updated_at: now,
      chatHistory: []
    };
    
    this.projects.set(projectId, project);
    this.currentProjectId = projectId;
    this.saveToStorage();
    
    console.log('🆕 TempProjectManager: 创建临时项目', {
      projectId,
      title: project.title
    });
    
    return projectId;
  }

  // 获取项目
  getProject(projectId: string): TempProject | null {
    return this.projects.get(projectId) || null;
  }

  // 获取所有项目
  getAllProjects(): TempProject[] {
    return Array.from(this.projects.values());
  }

  // 获取当前项目
  getCurrentProject(): TempProject | null {
    return this.currentProjectId ? this.getProject(this.currentProjectId) : null;
  }

  // 设置当前项目
  setCurrentProject(projectId: string): void {
    this.currentProjectId = projectId;
    this.saveToStorage();
  }

  // 更新项目
  updateProject(projectId: string, html_content: string, prompts?: string[]): boolean {
    const project = this.projects.get(projectId);
    if (!project) return false;
    
    project.html_content = html_content;
    project.updated_at = new Date().toISOString();
    
    if (prompts) {
      project.prompts = prompts;
    }
    
    this.projects.set(projectId, project);
    this.saveToStorage();
    
    console.log('📝 TempProjectManager: 更新项目', {
      projectId,
      htmlLength: html_content.length,
      promptsCount: project.prompts.length
    });
    
    return true;
  }

  // 添加聊天消息
  addChatMessage(projectId: string, type: 'user' | 'ai', content?: string, html_content?: string): string {
    const project = this.projects.get(projectId);
    if (!project) return '';
    
    const messageId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const message = {
      id: messageId,
      type,
      content,
      html_content,
      timestamp: new Date().toISOString(),
      isGenerating: type === 'ai' && !content && !html_content
    };
    
    project.chatHistory.push(message);
    project.updated_at = new Date().toISOString();
    
    this.projects.set(projectId, project);
    this.saveToStorage();
    
    console.log('💬 TempProjectManager: 添加聊天消息', {
      projectId,
      messageId,
      type,
      isGenerating: message.isGenerating
    });
    
    return messageId;
  }

  // 更新聊天消息
  updateChatMessage(projectId: string, messageId: string, content?: string, html_content?: string, isGenerating?: boolean): boolean {
    const project = this.projects.get(projectId);
    if (!project) return false;
    
    const message = project.chatHistory.find(msg => msg.id === messageId);
    if (!message) return false;
    
    if (content !== undefined) message.content = content;
    if (html_content !== undefined) message.html_content = html_content;
    if (isGenerating !== undefined) message.isGenerating = isGenerating;
    
    project.updated_at = new Date().toISOString();
    this.projects.set(projectId, project);
    this.saveToStorage();
    
    return true;
  }

  // 删除项目
  deleteProject(projectId: string): boolean {
    const deleted = this.projects.delete(projectId);
    
    if (deleted && this.currentProjectId === projectId) {
      this.currentProjectId = null;
    }
    
    this.saveToStorage();
    
    console.log('🗑️ TempProjectManager: 删除项目', {
      projectId,
      success: deleted
    });
    
    return deleted;
  }

  // 清空所有项目
  clearAllProjects(): void {
    this.projects.clear();
    this.currentProjectId = null;
    this.lastReminderTime = 0;
    this.reminderCount = 0;
    this.saveToStorage();
    
    console.log('🧹 TempProjectManager: 清空所有项目');
  }

  // 生成项目标题
  private generateTitle(firstMessage: string): string {
    const title = firstMessage.slice(0, 30);
    return title.length < firstMessage.length ? title + '...' : title;
  }

  // 获取统计信息
  getStats(): {
    totalProjects: number;
    totalMessages: number;
    storageUsed: number;
  } {
    const totalMessages = Array.from(this.projects.values())
      .reduce((total, project) => total + project.chatHistory.length, 0);
    
    return {
      totalProjects: this.projects.size,
      totalMessages,
      storageUsed: this.getStorageSize()
    };
  }

  // 获取localStorage使用大小（字节）
  private getStorageSize(): number {
    if (typeof window === 'undefined') return 0;
    
    try {
      const stored = localStorage.getItem('loomrun_temp_projects');
      return stored ? new Blob([stored]).size : 0;
    } catch {
      return 0;
    }
  }

  // 智能登录提醒 - 防骚扰版本
  showLoginReminder(): void {
    const now = Date.now();
    const stats = this.getStats();
    
    // 🛡️ 防骚扰检查
    const timeSinceLastReminder = now - this.lastReminderTime;
    const sessionDuration = now - this.sessionStartTime;
    
    // 智能判断是否需要提醒
    const shouldShowReminder = 
      stats.totalProjects > 0 && // 有临时项目
      this.hasUserInteracted && // 用户有交互行为
      sessionDuration > 5 * 60 * 1000 && // 会话超过5分钟
      timeSinceLastReminder > 10 * 60 * 1000 && // 距离上次提醒超过10分钟
      this.reminderCount < 3; // 每天最多提醒3次
    
    if (!shouldShowReminder) {
      console.log('🤫 智能防骚扰：跳过登录提醒', {
        hasProjects: stats.totalProjects > 0,
        hasInteracted: this.hasUserInteracted,
        sessionDuration: Math.round(sessionDuration / 1000),
        timeSinceLastReminder: Math.round(timeSinceLastReminder / 1000),
        reminderCount: this.reminderCount
      });
      return;
    }
    
    // 记录提醒
    this.lastReminderTime = now;
    this.reminderCount++;
    this.saveToStorage();
    
    toast.warning(
      `您有 ${stats.totalProjects} 个临时项目，建议登录保存`,
      {
        duration: 6000,
        position: "top-center",
        action: {
          label: "登录",
          onClick: () => {
            // 触发登录事件
            window.dispatchEvent(new CustomEvent('show-login-modal'));
          }
        }
      }
    );
    
    console.log('🔔 智能登录提醒已显示', {
      projectCount: stats.totalProjects,
      reminderCount: this.reminderCount
    });
  }

  // 检查是否有临时项目需要保存 - 智能版本
  hasUnsavedProjects(): boolean {
    return this.projects.size > 0;
  }

  // 智能检查是否需要页面退出警告
  shouldWarnOnExit(): boolean {
    const now = Date.now();
    const sessionDuration = now - this.sessionStartTime;
    const stats = this.getStats();
    
    // 只有在以下情况才警告：
    // 1. 有临时项目
    // 2. 会话时间超过3分钟（避免刚进入就警告）
    // 3. 有用户交互行为
    // 4. 项目有实际内容（不只是空项目）
    return (
      stats.totalProjects > 0 &&
      sessionDuration > 3 * 60 * 1000 &&
      this.hasUserInteracted &&
      stats.totalMessages > 1 // 至少有用户消息和AI回复
    );
  }
} 