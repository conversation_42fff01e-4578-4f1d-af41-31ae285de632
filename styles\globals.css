/* Version: 2024-12-19-v7 - Light gray user messages */
@import "tailwindcss";

/* 🚀 导入滚动性能优化样式 */
@import './scroll-optimization.css';

/* 🎯 任务列表划线动画 - 丝滑删除效果 */
@keyframes strikethrough {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* 🎯 新增：丝滑划线动画 */
@keyframes drawLine {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

/* 🎯 任务完成勾勾的装逼动画 */
@keyframes checkPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 🎯 AI状态文字渐变光影动画 - 专业级shimmer效果 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
      --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* 纯白色主题 - 成熟专业风格 */
  --background: oklch(1 0 0); /* 纯白色背景 */
  --foreground: oklch(0.25 0.015 45); /* 深灰棕色文字 */
  --card: oklch(0.995 0.005 0); /* 卡片背景 - 微灰白色 */
  --card-foreground: oklch(0.25 0.015 45);
  --popover: oklch(1 0 0); /* 弹窗背景 - 纯白色 */
  --popover-foreground: oklch(0.25 0.015 45);
  --primary: oklch(0.35 0.08 65); /* 温暖的主色调 */
  --primary-foreground: oklch(0.98 0.015 80);
  --secondary: oklch(0.97 0.005 0); /* 次要背景 - 浅灰白色 */
  --secondary-foreground: oklch(0.35 0.08 65);
  --muted: oklch(0.96 0.005 0); /* 柔和的背景色 */
  --muted-foreground: oklch(0.5 0.02 50);
  --accent: oklch(0.94 0.005 0); /* 强调色 - 浅灰色 */
  --accent-foreground: oklch(0.25 0.015 45);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.98 0.015 80);
  --border: oklch(0.9 0.005 0); /* 边框颜色 - 浅灰色 */
  --input: oklch(0.98 0.005 0); /* 输入框背景 */
  --ring: oklch(0.6 0.05 60);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.97 0.02 82);
  --sidebar-foreground: oklch(0.25 0.015 45);
  --sidebar-primary: oklch(0.35 0.08 65);
  --sidebar-primary-foreground: oklch(0.98 0.015 80);
  --sidebar-accent: oklch(0.92 0.025 75);
  --sidebar-accent-foreground: oklch(0.35 0.08 65);
  --sidebar-border: oklch(0.88 0.025 75);
  --sidebar-ring: oklch(0.6 0.05 60);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(0.985 0 0);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  /* 🎯 深色模式下项目侧边栏字体优化 */
  .dark .project-sidebar-professional {
    /* 增强文字对比度 */
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
  }
  
  .dark .project-sidebar-professional .text-foreground\/80 {
    color: rgba(255, 255, 255, 0.9) !important;
  }
  
  .dark .project-sidebar-professional .text-foreground\/60 {
    color: rgba(255, 255, 255, 0.75) !important;
  }
  
  /* 🎯 浅色模式聊天界面优化 */
  .light .enhanced-chat {
    /* 确保聊天界面在浅色模式下有足够的对比度 */
    background-color: rgb(255, 255, 255);
  }
  
  .light .message-wrapper {
    /* 优化消息容器在浅色模式下的可读性 */
  }
  
  .light .user-avatar-container {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .light .ai-avatar-container {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  /* 🎯 编辑模式浅色优化 */
  .light .editor-mode {
    /* 编辑模式在浅色主题下的优化 */
    background-color: rgb(249, 250, 251);
  }
  
  .light .style-panel-enhanced {
    /* 样式面板在浅色模式下的背景优化 */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border-color: rgba(229, 231, 235, 0.8);
  }
  
  .light .edit-mode-indicator {
    /* 编辑模式指示器在浅色模式下更明显 */
    color: rgb(59, 130, 246);
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  /* 🎯 编辑模式十字光标优化 */
  .light .edit-mode-active iframe {
    /* 浅色模式下的编辑光标样式 */
    cursor: crosshair !important;
  }
  
  .light .hovered-element {
    /* 浅色模式下悬停元素的样式 */
    outline: 2px dashed rgba(59, 130, 246, 0.8) !important;
    outline-offset: 2px !important;
    background-color: rgba(59, 130, 246, 0.1) !important;
  }
  
  /* 🎯 标签栏浅色模式优化 */
  .light .bg-secondary {
    /* 确保浅色模式下标签栏有足够对比度 */
    background-color: rgba(241, 245, 249, 0.8);
  }
  
  .light .border-blue-500 {
    /* 浅色模式下的蓝色边框更明显 */
    border-color: rgb(59, 130, 246) !important;
  }
  
  /* 🎯 样式面板输入框浅色优化 */
  .light .style-panel-input {
    background-color: rgb(255, 255, 255);
    border-color: rgb(209, 213, 219);
    color: rgb(17, 24, 39);
  }
  
  .light .style-panel-input:focus {
    border-color: rgb(59, 130, 246);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
  
  /* 🎯 样式面板按钮浅色优化 */
  .light .style-panel-button-secondary {
    background-color: rgb(255, 255, 255);
    border-color: rgb(209, 213, 219);
    color: rgb(75, 85, 99);
  }
  
  .light .style-panel-button-secondary:hover {
    background-color: rgb(249, 250, 251);
    border-color: rgb(156, 163, 175);
  }
  
  /* 🎯 样式面板标签页浅色优化 */
  .light .style-panel-tab.active {
    color: rgb(59, 130, 246);
    background-color: rgb(255, 255, 255);
    border-bottom-color: rgb(59, 130, 246);
  }
  
  .light .style-panel-tab:hover {
    background-color: rgba(59, 130, 246, 0.05);
    color: rgb(59, 130, 246);
  }
  
  /* 🎯 样式面板快速颜色按钮优化 */
  .light .style-panel-quick-color {
    border-color: rgb(209, 213, 219);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  
  .light .style-panel-quick-color:hover {
    border-color: rgb(59, 130, 246);
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  }
  
  /* 🎯 样式面板优化 */
  .style-panel-input {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  body {
    @apply bg-background text-foreground relative;
    /* 🔧 关键修复：确保只有一个滚动条 */
    min-height: 100vh;
    overflow-x: hidden; /* 隐藏水平滚动条 */
    overflow-y: auto; /* 只允许垂直滚动 */
  }
  html {
    @apply scroll-smooth;
  }
  
  /* LoomRun 大型背景文字 - 主要层 */
  body::before {
    content: 'LoomRun';
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-3deg);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: clamp(6rem, 20vw, 18rem);
    font-weight: 800;
    letter-spacing: -0.05em;
    color: transparent;
    background: linear-gradient(
      45deg,
      rgba(59, 130, 246, 0.15) 0%,
      rgba(147, 51, 234, 0.2) 25%,
      rgba(59, 130, 246, 0.18) 50%,
      rgba(16, 185, 129, 0.12) 75%,
      rgba(59, 130, 246, 0.16) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 
      0 0 40px rgba(59, 130, 246, 0.3),
      0 0 80px rgba(147, 51, 234, 0.2),
      0 0 120px rgba(16, 185, 129, 0.15);
    filter: blur(0.5px);
    pointer-events: none;
    z-index: -1;
    white-space: nowrap;
    animation: tech-glow 6s ease-in-out infinite alternate;
  }
  
  /* 高科技装饰层 - 第二个LoomRun */
  body::after {
    content: 'LoomRun';
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(2deg) scale(1.1);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: clamp(6rem, 20vw, 18rem);
    font-weight: 200;
    letter-spacing: 0.1em;
    color: transparent;
    background: linear-gradient(
      -45deg,
      rgba(59, 130, 246, 0.08) 0%,
      rgba(147, 51, 234, 0.12) 25%,
      rgba(16, 185, 129, 0.1) 50%,
      rgba(59, 130, 246, 0.06) 75%,
      rgba(147, 51, 234, 0.09) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: blur(2px);
    pointer-events: none;
    z-index: -2;
    white-space: nowrap;
    animation: tech-glow-secondary 8s ease-in-out infinite alternate-reverse;
  }
  
  /* 主要 LoomRun 文字动画 */
  @keyframes tech-glow {
    0% { 
      filter: blur(0.5px) brightness(0.9) hue-rotate(0deg);
      text-shadow: 
        0 0 30px rgba(59, 130, 246, 0.2),
        0 0 60px rgba(147, 51, 234, 0.15),
        0 0 90px rgba(16, 185, 129, 0.1);
      transform: translate(-50%, -50%) rotate(-3deg) scale(1);
    }
    100% { 
      filter: blur(1px) brightness(1.3) hue-rotate(10deg);
      text-shadow: 
        0 0 60px rgba(59, 130, 246, 0.4),
        0 0 120px rgba(147, 51, 234, 0.3),
        0 0 180px rgba(16, 185, 129, 0.2);
      transform: translate(-50%, -50%) rotate(-2deg) scale(1.02);
    }
  }
  
  /* 次要 LoomRun 文字动画 */
  @keyframes tech-glow-secondary {
    0% { 
      filter: blur(2px) brightness(0.7);
      transform: translate(-50%, -50%) rotate(2deg) scale(1.1);
    }
    100% { 
      filter: blur(3px) brightness(1.1);
      transform: translate(-50%, -50%) rotate(1deg) scale(1.12);
    }
  }
  
  /* 确保内容在背景之上 */
  #__next,
  main,
  .main-content {
    position: relative;
    z-index: 1;
  }
  
  /* 确保Popover能够正确显示 */
  [data-radix-popper-content-wrapper],
  [data-radix-portal],
  [data-radix-popover-content] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    z-index: 50 !important;
  }
  
  /* 高科技几何装饰层 */
  html::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -3;
    background: 
      /* 科技线条网格 */
      linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.05) 50%, transparent 100%) 0 0 / 100px 2px,
      linear-gradient(0deg, transparent 0%, rgba(147, 51, 234, 0.04) 50%, transparent 100%) 0 0 / 2px 100px,
      /* 对角线装饰 */
      linear-gradient(45deg, transparent 48%, rgba(16, 185, 129, 0.03) 50%, transparent 52%) 0 0 / 80px 80px,
      linear-gradient(-45deg, transparent 48%, rgba(59, 130, 246, 0.02) 50%, transparent 52%) 40px 40px / 80px 80px,
      /* 光晕效果 */
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.06) 0%, transparent 50%),
      radial-gradient(circle at 50% 10%, rgba(147, 51, 234, 0.05) 0%, transparent 40%),
      radial-gradient(circle at 10% 90%, rgba(59, 130, 246, 0.04) 0%, transparent 35%);
    background-size: 
      100px 2px,
      2px 100px,
      80px 80px,
      80px 80px,
      800px 800px,
      600px 600px,
      400px 400px,
      500px 500px;
    animation: tech-geometry 15s ease-in-out infinite alternate;
  }
  
  @keyframes tech-geometry {
    0% {
      opacity: 0.4;
      transform: translateY(0) rotate(0deg);
    }
    50% {
      opacity: 0.7;
      transform: translateY(-10px) rotate(0.5deg);
    }
    100% {
      opacity: 0.5;
      transform: translateY(-5px) rotate(-0.3deg);
    }
  }
  
  /* 隐藏滚动条的通用类 */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* 隐藏 Next.js 开发者面板和所有开发指示器 */
  [data-nextjs-dialog-overlay],
  [data-nextjs-dialog],
  [data-nextjs-dialog-left-right],
  [data-nextjs-dialog-backdrop],
  div[id^="__next-dev-indicator"],
  div[data-nextjs-dev-overlay] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
  
  /* 只隐藏特定的开发工具固定定位元素，但排除Popover */
  div[style*="position: fixed"][style*="bottom"][style*="left"]:not([data-radix-popper-content-wrapper]):not([data-radix-portal]) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
  
  /* 隐藏所有高层级z-index的固定定位元素（开发工具），但排除Popover */
  div[style*="position: fixed"][style*="z-index: 2147483647"]:not([data-radix-popper-content-wrapper]):not([data-radix-portal]),
  div[style*="position: fixed"][style*="z-index: 999999"]:not([data-radix-popper-content-wrapper]):not([data-radix-portal]),
  div[style*="position: fixed"][style*="z-index: 99999"]:not([data-radix-popper-content-wrapper]):not([data-radix-portal]) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
  
  /* 通过类名隐藏可能的开发工具 */
  .__next-dev-indicator,
  .__next-dev-overlay,
  [class*="__next-dev"],
  [class*="nextjs-dev"],
  [data-nextjs-dev] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
}

.background__noisy {
  @apply bg-blend-normal pointer-events-none opacity-90;
  background-size: 25ww auto;
  background-image: url("/background_noisy.webp");
  @apply fixed w-screen h-screen -z-1 top-0 left-0;
}

/* Monaco编辑器专业深灰色主题 - 与左侧历史项目栏一致 */
.monaco-editor-professional,
.monaco-editor-professional .monaco-editor,
.monaco-editor-professional .margin,
.monaco-editor-professional .monaco-editor-background,
.monaco-editor-professional .view-lines,
.monaco-editor-professional .monaco-editor-background {
  background-color: #171717 !important;
}

/* 传统Monaco编辑器样式保持 */
.monaco-editor,
.monaco-editor .margin,
.monaco-editor .monaco-editor-background,
.monaco-editor .view-lines,
.monaco-editor-background {
  background-color: #171717 !important;
}

.monaco-editor .line-numbers {
  color: #444444 !important;
}

.monaco-editor .current-line ~ .line-numbers {
  color: #bbbbbb !important;
}

.monaco-editor .view-line {
  background-color: #171717 !important;
}

/* 确保每一行的背景都是一致的深灰色 */
.monaco-editor .view-lines .view-line,
.monaco-editor .view-line span {
  background-color: #171717 !important;
}

/* 确保编辑器区域所有空隙都是统一的深灰色 */
.monaco-editor-professional {
  background-color: #171717 !important;
}

/* 确保编辑器容器和AI输入框容器背景一致 */
div[style*="background-color: rgb(23, 23, 23)"],
div[style*="background-color: #171717"] {
  background-color: #171717 !important;
}

/* 专门修复编辑器容器的间隙背景色问题 */
.relative.flex-1.overflow-hidden.h-full.flex.flex-col[style*="background-color: #171717"] {
  background-color: #171717 !important;
}

/* 确保Monaco编辑器和AskAI组件之间的gap显示正确的背景色 */
.relative.flex-1.overflow-hidden.h-full.flex.flex-col[style*="background-color: #171717"] > .monaco-editor-professional,
.relative.flex-1.overflow-hidden.h-full.flex.flex-col[style*="background-color: #171717"] > div[style*="background-color: #171717"] {
  background-color: #171717 !important;
}

.monaco-editor .selected-text {
  background-color: #264f78 !important;
}

.monaco-editor .cursor {
  background-color: #ffffff !important;
}

.matched-line {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-left: 2px solid #3b82f6 !important;
}

/* 确保Monaco编辑器的所有组件都是与左侧历史项目栏一致的深色背景 */
.monaco-editor,
.monaco-editor *,
.monaco-editor .view-overlays,
.monaco-editor .margin-view-overlays,
.monaco-editor .monaco-scrollable-element,
.monaco-editor .decorationsOverviewRuler,
.monaco-editor .monaco-editor-background,
.monaco-editor .margin,
.monaco-editor .view-line,
.monaco-editor .view-lines > .view-line {
  background-color: #171717 !important;
}

/* Monaco编辑器的滚动条样式 - 与侧边栏保持一致 */
.monaco-editor .scrollbar.vertical,
.monaco-editor .scrollbar.horizontal {
  background-color: transparent !important;
  width: 6px !important;
}

.monaco-editor .slider {
  background-color: rgba(115, 115, 115, 0.2) !important;
  border-radius: 3px !important;
  width: 6px !important;
}

.monaco-editor .slider:hover {
  background-color: rgba(115, 115, 115, 0.4) !important;
}

.monaco-editor .slider.active {
  background-color: rgba(115, 115, 115, 0.5) !important;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 只移除可能造成刻度线的特定样式，保留开关的基本功能 */
.follow-up-container::before,
.follow-up-container::after {
  content: none !important;
  display: none !important;
}

/* 移除可能的文本装饰线，但保留开关样式 */
.follow-up-container * {
  text-decoration: none !important;
  text-decoration-line: none !important;
}

/* 修复手机端历史项目列表的过度滚动问题 */
.overscroll-contain {
  overscroll-behavior: contain;
  overscroll-behavior-y: contain;
  -webkit-overflow-scrolling: touch;
}

/* 确保侧边栏在移动端的滚动边界正确 */
.sidebar-scroll-container {
  overscroll-behavior: contain;
  overscroll-behavior-y: contain;
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
}

/* 侧边栏滚动条样式 - 低调且一致 */
.sidebar-scroll-container .overflow-y-auto::-webkit-scrollbar,
.sidebar-scroll-container::-webkit-scrollbar,
.fixed.left-0.w-80 .overflow-y-auto::-webkit-scrollbar,
.fixed.left-0.w-80 *::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll-container .overflow-y-auto::-webkit-scrollbar-track,
.sidebar-scroll-container::-webkit-scrollbar-track,
.fixed.left-0.w-80 .overflow-y-auto::-webkit-scrollbar-track,
.fixed.left-0.w-80 *::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-scroll-container .overflow-y-auto::-webkit-scrollbar-thumb,
.sidebar-scroll-container::-webkit-scrollbar-thumb,
.fixed.left-0.w-80 .overflow-y-auto::-webkit-scrollbar-thumb,
.fixed.left-0.w-80 *::-webkit-scrollbar-thumb {
  background-color: rgba(115, 115, 115, 0.2);
  border-radius: 3px;
  border: none;
}

.sidebar-scroll-container .overflow-y-auto::-webkit-scrollbar-thumb:hover,
.sidebar-scroll-container::-webkit-scrollbar-thumb:hover,
.fixed.left-0.w-80 .overflow-y-auto::-webkit-scrollbar-thumb:hover,
.fixed.left-0.w-80 *::-webkit-scrollbar-thumb:hover {
  background-color: rgba(115, 115, 115, 0.4);
}

.sidebar-scroll-container .overflow-y-auto::-webkit-scrollbar-thumb:active,
.sidebar-scroll-container::-webkit-scrollbar-thumb:active,
.fixed.left-0.w-80 .overflow-y-auto::-webkit-scrollbar-thumb:active,
.fixed.left-0.w-80 *::-webkit-scrollbar-thumb:active {
  background-color: rgba(115, 115, 115, 0.5);
}

.sidebar-scroll-container .overflow-y-auto::-webkit-scrollbar-corner,
.sidebar-scroll-container::-webkit-scrollbar-corner,
.fixed.left-0.w-80 .overflow-y-auto::-webkit-scrollbar-corner,
.fixed.left-0.w-80 *::-webkit-scrollbar-corner {
  background: transparent;
}

/* 移动端专用：防止过度滚动显示黑色区域 + 项目栏全屏优化 */
@media (max-width: 1024px) {
  .overscroll-contain {
    overscroll-behavior: none;
    overscroll-behavior-y: none;
    scroll-behavior: smooth;
  }

  .sidebar-scroll-container {
    overscroll-behavior: none;
    overscroll-behavior-y: none;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: none;
  }
}

/* 移动端触摸优化 */
@media (max-width: 1024px) {
  /* 移动端按钮触摸优化 */
  .touch-manipulation {
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
  }

  /* 确保移动端侧边栏按钮可点击 */
  button {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* 汉堡按钮专业优化 */
  .hamburger-button {
    /* 确保足够的点击区域 */
    min-width: 44px !important;
    min-height: 44px !important;

    /* 现代化圆角 */
    border-radius: 12px !important;

    /* 微妙的阴影效果 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

    /* 平滑过渡 */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  .hamburger-button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
  }

  .hamburger-button:active {
    transform: translateY(0) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }
}



/* 增强的overscroll-none样式 */
.overscroll-none {
  overscroll-behavior: none !important;
  overscroll-behavior-y: none !important;
  overscroll-behavior-x: none !important;
}

/* 侧边栏滚动边界严格控制 */
.sidebar-scroll-container {
  scroll-behavior: smooth;
  overscroll-behavior: none;
  overscroll-behavior-y: none;
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
  scroll-snap-type: none;
}

/* 温和移除刻度线，保留开关的基本样式 */
.follow-up-container {
  border-image: none !important;
  text-decoration: none !important;
  text-decoration-line: none !important;
  background-image: none !important;
}

/* 只针对性地移除Switch的边框问题，保留其背景色 */
#follow-up-switch {
  outline: none !important;
  outline-style: none !important;
  outline-width: 0 !important;
}

/* 自定义中性色 */
.bg-neutral-850 {
  background-color: #1a1a1a;
}

.bg-neutral-750 {
  background-color: #2a2a2a;
}

.from-neutral-850 {
  --tw-gradient-from: #1a1a1a var(--tw-gradient-from-position);
}

.to-neutral-850 {
  --tw-gradient-to: #1a1a1a var(--tw-gradient-to-position);
}

.from-neutral-750 {
  --tw-gradient-from: #2a2a2a var(--tw-gradient-from-position);
}

.to-neutral-800 {
  --tw-gradient-to: #262626 var(--tw-gradient-to-position);
}

/* AI修改代码高亮样式 */
.ai-modified-line {
  background-color: rgba(34, 197, 94, 0.1) !important;
  border-left: 3px solid #22c55e !important;
}

.ai-modified-glyph {
  background-color: #22c55e !important;
  width: 4px !important;
}

.ai-modified-decoration {
  background-color: rgba(34, 197, 94, 0.2) !important;
  width: 8px !important;
}

.ai-modified-inline {
  background-color: rgba(34, 197, 94, 0.05) !important;
}

/* Monaco编辑器专业样式增强 */
.monaco-editor-professional {
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'DejaVu Sans Mono', monospace !important;
}

.monaco-editor-professional .view-lines {
  padding-top: 8px;
  padding-bottom: 8px;
}

/* 修改历史高亮样式（保留原有的matched-line类） */
.matched-line {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-left: 2px solid #3b82f6 !important;
}

/* 项目切换动画效果 */
.project-switching-overlay {
  animation: fadeInOverlay 0.2s ease-in-out;
}

.project-switching-content {
  animation: slideInUp 0.3s ease-out;
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 🎯 项目加载页面增强动画 */
.project-loading-container {
  animation: fadeInScale 0.4s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 🎨 加载动画光晕效果 */
.loading-spinner-glow {
  position: relative;
}

.loading-spinner-glow::before {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  animation: spin 2s linear infinite;
  z-index: -1;
}

/* 🎯 浅色模式下的加载动画优化 */
.light .loading-spinner-glow::before {
  background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.2), transparent);
}

/* 🎯 深色模式下的加载动画优化 */
.dark .loading-spinner-glow::before {
  background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.4), transparent);
}

/* 项目切换成功的反馈动画 */
.project-switch-success {
  animation: successPulse 0.5s ease-in-out;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* 编辑器内容切换动画 */
.editor-content-transition {
  transition: opacity 0.2s ease-in-out;
}

.editor-content-switching {
  opacity: 0.7;
}

/* 版本控制动画效果 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes success-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
  }
}

@keyframes completion-celebration {
  0% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.05);
    filter: brightness(1.2);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-success-pulse {
  animation: success-pulse 2s infinite;
}

.animate-completion-celebration {
  animation: completion-celebration 0.8s ease-out;
}

/* 聊天区域专业滚动条样式 */
.chat-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #525252;
  width: 6px;
}

/* Firefox滚动条样式 */
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #404040 transparent;
}

/* 平滑滚动优化 */
.chat-scrollbar {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

/* VS Code风格滚动条 */
.vscode-scrollbar::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.vscode-scrollbar::-webkit-scrollbar-track {
  background: #1e1e1e;
}

.vscode-scrollbar::-webkit-scrollbar-thumb {
  background: #424242;
  border-radius: 0;
  border: 3px solid #1e1e1e;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #4f4f4f;
}

.vscode-scrollbar::-webkit-scrollbar-corner {
  background: #1e1e1e;
}

/* Firefox VS Code风格滚动条 */
.vscode-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #424242 #1e1e1e;
}

/* VS Code代码选择样式 */
.vscode-code ::selection {
  background: #264f78;
  color: #d4d4d4;
}

/* VS Code代码高亮样式 */
.vscode-code {
  font-feature-settings: "liga" 1, "calt" 1;
}

/* 行号悬停效果 */
.line-number:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 代码行悬停效果 */
.code-line:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* 聊天消息区域高度限制 - 为AI输入框预留空间 */
.chat-messages-height-limit {
  /* 让flex布局自动计算高度，确保与编辑器模式一致 */
  flex-shrink: 1;
  flex-grow: 1;
  overflow-y: auto;
  scroll-behavior: smooth;
  overscroll-behavior: contain;
  min-height: 0; /* 允许flex shrink */
}

/* 统一的输入框底部间距 - 适用于所有设备 */
.chat-input-bottom-spacing {
  padding-bottom: 1.5rem; /* 24px 专业间距 */
}

/* 移动端输入框底部间距增强 */
@media (max-width: 768px) {
  .chat-input-bottom-spacing {
    padding-bottom: 2rem; /* 32px 移动端增强间距 */
  }
}

/* 单个聊天消息限制，防止超长内容撑破布局 */
.chat-message-constrained {
  max-height: 300px;
  overflow-y: auto;
  word-wrap: break-word;
  word-break: break-word;
}

/* AI输入框容器 - 专业间距设计 */
.ai-input-container {
  flex-shrink: 0;
  flex-grow: 0;
  padding: 1rem;
  padding-bottom: 1.5rem; /* 24px 底部间距 */
}

/* 编辑器模式下的AI输入框位置调整 */
.editor-mode .ai-input-container {
  padding-bottom: 1.5rem; /* 保持一致的底部间距 */
}

/* 🎯 通用AI输入框滚动条隐藏 - 精准针对所有AI输入框 */
.ai-input-container textarea,
.ai-input-container input,
textarea[placeholder*="告诉我"],
textarea[placeholder*="修改"],
textarea[placeholder*="添加"],
textarea[placeholder*="继续"],
input[placeholder*="输入消息"],
input[placeholder*="告诉我"],
/* 🎯 按容器类名精准定位AI输入框 */
.ask-ai-component textarea,
.ask-ai-component input,
.enhanced-chat textarea,
.enhanced-chat input,
.contact-chat input,
.welcome-layout textarea,
.welcome-layout input {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.ai-input-container textarea::-webkit-scrollbar,
.ai-input-container input::-webkit-scrollbar,
textarea[placeholder*="告诉我"]::-webkit-scrollbar,
textarea[placeholder*="修改"]::-webkit-scrollbar,
textarea[placeholder*="添加"]::-webkit-scrollbar,
textarea[placeholder*="继续"]::-webkit-scrollbar,
input[placeholder*="输入消息"]::-webkit-scrollbar,
input[placeholder*="告诉我"]::-webkit-scrollbar,
.ask-ai-component textarea::-webkit-scrollbar,
.ask-ai-component input::-webkit-scrollbar,
.enhanced-chat textarea::-webkit-scrollbar,
.enhanced-chat input::-webkit-scrollbar,
.contact-chat input::-webkit-scrollbar,
.welcome-layout textarea::-webkit-scrollbar,
.welcome-layout input::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.ai-input-container textarea::-webkit-scrollbar-track,
.ai-input-container input::-webkit-scrollbar-track,
textarea[placeholder*="告诉我"]::-webkit-scrollbar-track,
textarea[placeholder*="修改"]::-webkit-scrollbar-track,
textarea[placeholder*="添加"]::-webkit-scrollbar-track,
textarea[placeholder*="继续"]::-webkit-scrollbar-track,
input[placeholder*="输入消息"]::-webkit-scrollbar-track,
input[placeholder*="告诉我"]::-webkit-scrollbar-track,
.ask-ai-component textarea::-webkit-scrollbar-track,
.ask-ai-component input::-webkit-scrollbar-track,
.enhanced-chat textarea::-webkit-scrollbar-track,
.enhanced-chat input::-webkit-scrollbar-track,
.contact-chat input::-webkit-scrollbar-track,
.welcome-layout textarea::-webkit-scrollbar-track,
.welcome-layout input::-webkit-scrollbar-track {
  display: none !important;
}

.ai-input-container textarea::-webkit-scrollbar-thumb,
.ai-input-container input::-webkit-scrollbar-thumb,
textarea[placeholder*="告诉我"]::-webkit-scrollbar-thumb,
textarea[placeholder*="修改"]::-webkit-scrollbar-thumb,
textarea[placeholder*="添加"]::-webkit-scrollbar-thumb,
textarea[placeholder*="继续"]::-webkit-scrollbar-thumb,
input[placeholder*="输入消息"]::-webkit-scrollbar-thumb,
input[placeholder*="告诉我"]::-webkit-scrollbar-thumb,
.ask-ai-component textarea::-webkit-scrollbar-thumb,
.ask-ai-component input::-webkit-scrollbar-thumb,
.enhanced-chat textarea::-webkit-scrollbar-thumb,
.enhanced-chat input::-webkit-scrollbar-thumb,
.contact-chat input::-webkit-scrollbar-thumb,
.welcome-layout textarea::-webkit-scrollbar-thumb,
.welcome-layout input::-webkit-scrollbar-thumb {
  display: none !important;
}

.ai-input-container textarea::-webkit-scrollbar-corner,
.ai-input-container input::-webkit-scrollbar-corner,
textarea[placeholder*="告诉我"]::-webkit-scrollbar-corner,
textarea[placeholder*="修改"]::-webkit-scrollbar-corner,
textarea[placeholder*="添加"]::-webkit-scrollbar-corner,
textarea[placeholder*="继续"]::-webkit-scrollbar-corner,
input[placeholder*="输入消息"]::-webkit-scrollbar-corner,
input[placeholder*="告诉我"]::-webkit-scrollbar-corner,
.ask-ai-component textarea::-webkit-scrollbar-corner,
.ask-ai-component input::-webkit-scrollbar-corner,
.enhanced-chat textarea::-webkit-scrollbar-corner,
.enhanced-chat input::-webkit-scrollbar-corner,
.contact-chat input::-webkit-scrollbar-corner,
.welcome-layout textarea::-webkit-scrollbar-corner,
.welcome-layout input::-webkit-scrollbar-corner {
  display: none !important;
}

/* 选中元素标签的专业样式 */
.selected-element-tag {
  position: relative;
  overflow: hidden;
}

.selected-element-tag::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  border-radius: inherit;
  transition: all 0.3s ease;
}

.selected-element-tag:hover::before {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.2) 100%);
}

/* 添加微妙的动画效果 */
@keyframes element-tag-appear {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.element-tag-animation {
  animation: element-tag-appear 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 聊天界面优化样式 */

/* 编织纹理背景 */
.chat-container {
  background-image: 
    linear-gradient(45deg, rgba(255,255,255,0.015) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255,255,255,0.015) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.015) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.015) 75%);
  background-size: 16px 16px;
  background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}

/* 用户消息：无背景样式，直接显示在背景上 */
.user-message {
  /* 移除所有背景样式，让消息直接显示在聊天背景上 */
}

/* AI消息：专业卡片设计 */
.ai-message {
  justify-self: start;
  align-self: start;
  background: rgba(28, 28, 28, 0.95);
  border: 1px solid rgba(64, 64, 64, 0.4);
  border-radius: 8px;
  backdrop-filter: blur(12px);
  max-width: 90%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  position: relative; /* 为版本号标签提供定位基准 */
}

/* AI消息头部 */
.ai-message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(64, 64, 64, 0.4);
  background: rgba(28, 28, 28, 0.6);
  border-radius: 12px 12px 0 0;
}

/* AI消息内容区 */
.ai-message-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

/* AI头像呼吸动画 */
.ai-avatar {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: ai-breathing 2s ease-in-out infinite;
  overflow: hidden;
}

.ai-avatar img {
  width: 20px;
  height: 20px;
  object-fit: contain;
  filter: brightness(1.1) contrast(1.1);
}

@keyframes ai-breathing {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% { 
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

/* AI思考动画 */
.ai-thinking {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  color: #94a3b8;
}

.thinking-dots {
  display: flex;
  gap: 4px;
}

.thinking-dot {
  width: 6px;
  height: 6px;
  background: #3b82f6;
  border-radius: 50%;
  animation: thinking-pulse 1.4s ease-in-out infinite both;
}

.thinking-dot:nth-child(1) { animation-delay: -0.32s; }
.thinking-dot:nth-child(2) { animation-delay: -0.16s; }
.thinking-dot:nth-child(3) { animation-delay: 0s; }

@keyframes thinking-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 真实代码生成监控区域 */
.real-time-code-monitor {
  background: rgba(16, 16, 16, 0.95);
  border: 1px solid rgba(64, 64, 64, 0.3);
  border-radius: 6px;
  padding: 12px 16px;
  margin: 0;
  position: relative;
  overflow: hidden;
}

/* 兼容旧样式 */
.code-generation-progress {
  background: rgba(16, 16, 16, 0.95);
  border: 1px solid rgba(64, 64, 64, 0.3);
  border-radius: 6px;
  padding: 12px 16px;
  margin: 0;
  position: relative;
  overflow: hidden;
}

.code-generation-progress::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  animation: scanning-line 2s ease-in-out infinite;
}

.progress-bar {
  height: 2px;
  background: rgba(64, 64, 64, 0.3);
  border-radius: 1px;
  overflow: hidden;
  position: relative;
  margin-top: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06d6a0);
  border-radius: 1px;
  transition: width 0.3s ease-out;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06d6a0);
  border-radius: 1px;
  width: 0%;
  animation: progress-fill 3s ease-out infinite;
}

@keyframes scanning-line {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

@keyframes progress-fill {
  0% { width: 0%; }
  30% { width: 45%; }
  70% { width: 80%; }
  100% { width: 100%; }
}

/* 打字机效果 */
.typewriter-effect {
  overflow: hidden;
  border-right: 2px solid #3b82f6;
  animation: typewriter-cursor 1s infinite;
}

@keyframes typewriter-cursor {
  from { border-color: #3b82f6; }
  to { border-color: transparent; }
}

/* 聊天容器样式 */
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
}

/* 聊天滚动条优化 */
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #404040 transparent;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #525252;
  width: 6px;
}

/* 移动端聊天优化 */
@media (max-width: 768px) {
  .user-message {
    max-width: 85%;
    border-radius: 16px 16px 16px 4px;
    padding: 10px 14px;
  }
  
  .ai-message {
    max-width: 90%;
  }
  
  .ai-message-content {
    max-height: 300px;
    padding: 12px;
  }
  
  .ai-message-header {
    padding: 10px 12px;
  }
  
  .thinking-dots {
    gap: 3px;
  }
  
  .thinking-dot {
    width: 5px;
    height: 5px;
  }
  
  .ai-avatar {
    width: 28px;
    height: 28px;
  }
  
  .ai-avatar img {
    width: 18px;
    height: 18px;
  }
}

/* 动态代码生成效果 */
.code-generation-live {
  background: rgba(12, 12, 12, 0.98);
  border: 1px solid rgba(64, 64, 64, 0.4);
  border-radius: 8px;
  margin: 12px 0;
  overflow: hidden;
  position: relative;
}

.code-generation-live::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  animation: code-scanning 2s ease-in-out infinite;
}

.code-generation-header {
  background: rgba(20, 20, 20, 0.95);
  padding: 8px 16px;
  border-bottom: 1px solid rgba(64, 64, 64, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
}

.code-generation-body {
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, monospace;
  font-size: 12px;
  line-height: 1.6;
}

.code-line {
  margin: 2px 0;
  padding: 2px 0;
  border-left: 2px solid transparent;
  padding-left: 8px;
  transition: all 0.3s ease;
}

.code-line.active {
  border-left-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  animation: code-line-highlight 0.5s ease-out;
}

.code-line.completed {
  color: #10b981;
  opacity: 0.8;
}

/* 紧凑版代码生成效果 */
.real-time-code-monitor-compact {
  margin: 12px 0;
}

.code-generation-compact {
  background: rgba(12, 12, 12, 0.98);
  border: 1px solid rgba(64, 64, 64, 0.4);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.code-generation-compact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  animation: code-scanning 1.5s ease-in-out infinite;
}

.code-generation-header-compact {
  background: rgba(20, 20, 20, 0.95);
  padding: 6px 12px;
  border-bottom: 1px solid rgba(64, 64, 64, 0.3);
  display: flex;
  align-items: center;
  gap: 6px;
}

.code-generation-body-compact {
  padding: 8px 12px;
  max-height: 80px;
  overflow: hidden;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, monospace;
  font-size: 11px;
  line-height: 1.4;
}

.code-line-compact {
  margin: 1px 0;
  padding: 1px 0;
  border-left: 1px solid transparent;
  padding-left: 4px;
  transition: all 0.2s ease;
  opacity: 0.8;
}

.code-line-compact:last-child {
  border-left-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  opacity: 1;
  animation: code-line-highlight-compact 0.3s ease-out;
}

@keyframes code-line-highlight-compact {
  0% { 
    background: rgba(59, 130, 246, 0.15);
    transform: translateX(2px);
  }
  100% { 
    background: rgba(59, 130, 246, 0.05);
    transform: translateX(0);
  }
}

@keyframes code-scanning {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(100vw); }
  100% { transform: translateX(100vw); }
}

@keyframes code-line-highlight {
  0% { background: rgba(59, 130, 246, 0.2); }
  100% { background: rgba(59, 130, 246, 0.05); }
}

/* 代码预览区域样式增强 */
.ai-message-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, monospace;
  line-height: 1.5;
}

/* 消息动画进入效果 */
.message-wrapper {
  animation: message-slide-in 0.3s ease-out;
}

@keyframes message-slide-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* AI消息完成状态动画 */
.ai-message.completed {
  animation: ai-message-complete 0.5s ease-out;
}

@keyframes ai-message-complete {
  0% {
    transform: scale(0.98);
  }
  50% {
    transform: scale(1.01);
  }
  100% {
    transform: scale(1);
  }
}

/* =================================
   精细深色滚动条样式 - 全局优化
   ================================= */

/* 超细滚动条 - 主要用于项目侧边栏 */
.scrollbar-thin::-webkit-scrollbar {
  width: 3px;
  height: 3px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(115, 115, 115, 0.4);
  border-radius: 2px;
  transition: all 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(115, 115, 115, 0.7);
}

.scrollbar-thin::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条支持 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(115, 115, 115, 0.4) transparent;
}

/* 深色主题滚动条色彩自定义 */
.scrollbar-track-neutral-900::-webkit-scrollbar-track {
  background: rgb(23, 23, 23);
}

.scrollbar-thumb-neutral-700::-webkit-scrollbar-thumb {
  background: rgb(64, 64, 64);
}

.hover\:scrollbar-thumb-neutral-600:hover::-webkit-scrollbar-thumb {
  background: rgb(82, 82, 82);
}

/* 项目侧边栏专用滚动条优化 */
.project-sidebar-scroll::-webkit-scrollbar {
  width: 2px;
}

.project-sidebar-scroll::-webkit-scrollbar-track {
  background: rgba(23, 23, 23, 0.5);
}

.project-sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(64, 64, 64, 0.6);
  border-radius: 1px;
}

.project-sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(82, 82, 82, 0.8);
}

/* 确保底部有足够间距，避免贴底 */
.sidebar-content-spacing {
  padding-bottom: 1.5rem;
  margin-bottom: 0.75rem;
}

/* 响应式布局 - 移动端全屏覆盖优化（已整合到mobile-sidebar-fullscreen类中） */
@media (max-width: 1024px) {
  /* 确保移动端全屏侧边栏的额外样式 */
  .mobile-sidebar-fullscreen {
    /* 确保完全覆盖 */
    min-width: 100vw !important;
    min-height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
  }
}

@media (min-width: 1025px) {
  .project-sidebar-container {
    width: 200px !important;
    border-bottom: 2px solid rgb(82, 82, 82) !important; /* 强化底部边界 */
  }
}

/* 确保项目栏专业精简 - v0.dev风格边界强化 */
.project-sidebar-container {
  margin: 0;
  padding: 0;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 -2px 0 rgb(82, 82, 82); /* 内阴影底部边界 */
}

/* 项目栏内容精简布局 - v0.dev风格极致紧凑 */
.sidebar-content-compact {
  padding: 0.125rem;
  margin: 0;
  line-height: 1.2;
  position: relative;
}

/* 项目栏底部渐变淡出效果 - 提供视觉缓冲 */
.sidebar-content-compact::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rem;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(10, 10, 10, 0.3) 70%,
    rgba(10, 10, 10, 0.6) 100%
  );
  pointer-events: none;
  z-index: 1;
}

/* 项目栏底部渐变边界 - v0.dev风格专业感 增强版 */
.project-sidebar-container::after {
  content: '';
  position: absolute;
  bottom: -2px; /* 贴合底部边框 */
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(
    to right,
    rgba(82, 82, 82, 0.8) 0%,
    rgba(115, 115, 115, 0.9) 50%,
    rgba(82, 82, 82, 0.8) 100%
  );
  border-radius: 0 0 2px 2px;
  z-index: 1;
}

/* 确保项目栏紧贴顶部 - 零空隙精确定位 */
.project-sidebar-container {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* 超紧凑布局间距 */
.sidebar-content-spacing > * {
  margin-bottom: 0.125rem !important;
}

.sidebar-content-spacing > *:last-child {
  margin-bottom: 0 !important;
}

/* 🎯 项目栏专用滚动条样式 - v0.dev风格 */
.project-sidebar-scroll::-webkit-scrollbar {
  width: 4px;
}

.project-sidebar-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.project-sidebar-scroll::-webkit-scrollbar-thumb {
  background-color: #52525b;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.project-sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background-color: #71717a;
}

.project-sidebar-scroll::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
.project-sidebar-scroll {
  scrollbar-width: thin;
  scrollbar-color: #52525b transparent;
}

/* 🎯 编辑模式 - 十字光标样式 (类似v0.dev) */
.edit-mode-crosshair {
  cursor: crosshair !important;
}

.edit-mode-crosshair * {
  cursor: crosshair !important;
}

/* 🎯 编辑模式 - iframe内容区域十字光标 */
.preview-container-optimized.edit-mode-active {
  cursor: crosshair !important;
}

.preview-container-optimized.edit-mode-active iframe {
  cursor: crosshair !important;
}

/* 🎯 深蓝色高亮框已移除 - 现在只使用十字光标和iframe内的虚线框 */

/* 🔧 项目栏专业定位样式 - 精准修正：紧贴系统真实顶部栏下边缘，不受聊天区标签栏影响 */
.project-sidebar-professional {
  position: fixed !important;
  top: 44px !important; /* 🎯 精准修正：系统真实顶部栏高度44px，不是聊天区上方的切换栏 */
  left: 0 !important;
  height: calc(100vh - 44px) !important; /* 🎯 精准修正：减去系统真实顶部栏高度44px */
  z-index: 40 !important;
  transform: translateZ(0); /* 启用硬件加速 */
  will-change: transform; /* 优化transform性能 */
  margin: 0 !important;
  padding: 0 !important;
}

.project-sidebar-overlay-professional {
  position: fixed !important;
  top: 44px !important; /* 🎯 精准修正：系统真实顶部栏高度44px，不是聊天区上方的切换栏 */
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 30 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 🎯 编辑模式 - 禁用默认选择行为 */
.edit-mode-active * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 🎯 编辑模式 - iframe内部元素悬停效果 */
.hovered-element {
  outline: 2px dashed rgba(59, 130, 246, 0.6) !important;
  outline-offset: 2px !important;
  background-color: rgba(59, 130, 246, 0.08) !important;
  transition: all 0.15s ease-out !important;
}

/* 🎯 选择动画已移除 - 简化视觉反馈 */

/* 清理旧的重复样式 - 已移除 */

/* 🎨 样式面板美化 - 现代化设计 */
.style-panel-enhanced {
  background: linear-gradient(135deg, rgba(23, 23, 23, 0.95), rgba(38, 38, 38, 0.9));
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 🎯 样式面板输入框美化 */
.style-panel-input {
  background: rgba(38, 38, 38, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.style-panel-input:focus {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  background: rgba(38, 38, 38, 0.8);
}

/* 🎯 样式面板优化完成 - 滑条已移除，专注于美观的输入框设计 */

/* 🚀 首页输入框现代化样式优化 */
.welcome-layout .ai-input-container {
  /* 添加微妙的动画效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.welcome-layout .ai-input-container:hover {
  /* 悬停时的微妙提升效果 */
  transform: translateY(-1px);
}

.welcome-layout .ai-input-container:focus-within {
  /* 聚焦时的增强效果 */
  transform: translateY(-2px);
}

/* 优化输入框文字渲染 */
.welcome-layout textarea {
  /* 更好的文字渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 发送按钮的脉冲动画 */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }
  50% {
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.15) inset;
  }
}

.welcome-layout button[title="发送"]:not(:disabled):hover {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 🚀 首页响应式布局优化 */

/* 移动端优化 */
@media (max-width: 640px) {
  .welcome-layout {
    /* 移动端减少动画和变换 */
  }

  /* 主要内容区域移动端优化 */
  .welcome-layout .flex.flex-col.items-center.justify-start {
    transform: translateY(15vh) !important;
    min-height: 25vh !important;
    padding-top: 1rem !important;
    justify-content: flex-start !important;
  }

  /* 社区展示区域外层容器移动端优化 - 向下移动14% */
  .welcome-layout div[class*="mt-4"][class*="sm:mt-8"][class*="relative"][class*="z-20"] {
    transform: translateY(14vh) !important; /* 从15vh减少到14vh，向上移动1% */
  }

  /* 主标题移动端优化 - 向下移动靠近输入框，字体更大 */
  .welcome-layout h1 {
    /* 向下移动，减少与输入框的距离 - 减少50% */
    margin-bottom: 0.5rem !important; /* 从1rem减少到0.5rem */
    /* 字体大小增加 */
    font-size: 2rem !important; /* 32px，比原来的text-2xl(24px)更大 */
    line-height: 1.1 !important;
  }

  /* 标题容器移动端优化 */
  .welcome-layout .w-full.flex.justify-center.px-4.mb-8 {
    margin-bottom: 0.5rem !important; /* 从1rem减少到0.5rem，减少50%间距 */
  }

  /* 快捷按钮移动端优化 */
  .welcome-layout .flex.flex-wrap.items-center.justify-center {
    gap: 0.5rem !important;
  }
}

/* 平板端优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .welcome-layout .flex.flex-col.items-center.justify-center {
    transform: translateY(-6%) scale(1.08) !important;
    min-height: 55vh !important;
  }

  .welcome-layout .community-cards-container {
    transform: translateY(-2%) !important; /* 从-1%调整到-2%，向上移动1% */
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  .welcome-layout .flex.flex-col.items-center.justify-center {
    transform: translateY(-8%) scale(1.1) !important;
    min-height: 60vh !important;
  }

  .welcome-layout .community-cards-container {
    transform: translateY(-4%) !important; /* 从-3%调整到-4%，向上移动1% */
  }
}

/* 🎯 社区项目网格响应式优化 */
.community-grid {
  /* 确保网格在所有设备上都有合适的间距 */
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .community-grid {
    gap: 1rem;
  }
}

/* 🎯 社区展示区域移动端位置优化 */
.community-section-mobile-optimized {
  transform: translateY(12vh); /* 从13vh减少到12vh，向上移动1% */
  transform-origin: center;
}

@media (max-width: 640px) {
  .community-section-mobile-optimized {
    /* 移动端大幅减少向下移动距离 */
    transform: translateY(1vh) !important;
  }

  .community-cards-container {
    /* 移动端减少上边距 */
    margin-top: 0.5rem !important;
    /* 移动端进一步减少向下移动 */
    transform: translateY(0) !important;
  }

  /* 社区标题文字优化 */
  .community-cards-container h2 {
    font-size: 0.875rem !important; /* 14px */
    line-height: 1.25 !important;
    font-weight: 600 !important;
  }

  .community-cards-container p {
    font-size: 0.75rem !important; /* 12px */
    line-height: 1.4 !important;
    opacity: 0.8;
  }

  /* 浏览全部按钮优化 */
  .community-cards-container button {
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
  }
}

@media (min-width: 1024px) {
  .community-grid {
    gap: 1rem;
  }
}

/* 社区卡片响应式优化 */
.community-card {
  /* 移动端优化 */
  transition: all 0.2s ease;
}

@media (max-width: 640px) {
  .community-card {
    /* 移动端减少悬停效果 */
    transform: none !important;
  }

  .community-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  }
}

@media (min-width: 641px) {
  .community-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

/* 🚀 超小屏幕优化 (iPhone SE等) */
@media (max-width: 375px) {
  .welcome-layout .flex.flex-col.items-center.justify-center {
    transform: translateY(-1%) scale(1.01) !important;
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  /* 标题字体在超小屏幕上进一步缩小 */
  .welcome-layout h1 {
    transform: scale(1.1) !important;
  }

  /* 快捷按钮在超小屏幕上更紧凑 */
  .welcome-layout .flex.flex-wrap.items-center.justify-center {
    gap: 0.375rem !important;
  }

  .welcome-layout .flex.flex-wrap.items-center.justify-center button {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    font-size: 0.6875rem !important;
  }
}

/* 🎯 横屏手机优化 */
@media (max-height: 500px) and (orientation: landscape) {
  .welcome-layout .flex.flex-col.items-center.justify-center {
    min-height: 40vh !important;
    padding-top: 0.5rem !important;
    transform: translateY(-1%) scale(1.02) !important;
  }

  .welcome-layout .community-cards-container {
    transform: translateY(-2%) !important;
  }
}

/* 属性面板优化样式 */
.style-panel-container {
  /* 确保面板内容紧凑 */
  --panel-spacing: 0.5rem;
  --panel-item-height: 1.5rem;
  --panel-text-sm: 0.6875rem;
  --panel-text-xs: 0.625rem;
}

/* 优化选择器下拉菜单 */
.style-panel-select {
  min-height: var(--panel-item-height);
  font-size: var(--panel-text-xs);
}

/* 优化数值调节器 */
.style-panel-number-adjuster {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  height: var(--panel-item-height);
}

.style-panel-number-adjuster input {
  width: 2.5rem;
  text-align: center;
  font-size: var(--panel-text-xs);
  line-height: 1.2;
}

.style-panel-number-adjuster button {
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: all 0.15s ease;
}

.style-panel-number-adjuster button:hover {
  background-color: rgba(115, 115, 115, 0.3);
  transform: scale(1.05);
}

/* 优化颜色选择器 */
.style-panel-color-picker {
  width: 1.5rem;
  height: var(--panel-item-height);
  border-radius: 0.25rem;
  border: 1px solid rgba(115, 115, 115, 0.3);
  cursor: pointer;
  transition: all 0.15s ease;
}

.style-panel-color-picker:hover {
  border-color: rgba(59, 130, 246, 0.5);
  transform: scale(1.05);
}

/* 优化输入框 */
.style-panel-input {
  height: var(--panel-item-height);
  font-size: var(--panel-text-xs);
  padding: 0.25rem 0.5rem;
  background-color: rgba(38, 38, 38, 0.5);
  border: 1px solid rgba(115, 115, 115, 0.3);
  border-radius: 0.25rem;
  color: white;
  transition: all 0.15s ease;
}

.style-panel-input:focus {
  border-color: rgba(59, 130, 246, 0.5);
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 优化按钮组 */
.style-panel-button-group {
  display: flex;
  gap: 0.375rem;
  margin-top: 0.25rem;
}

.style-panel-button {
  height: 1.75rem;
  padding: 0 0.5rem;
  font-size: var(--panel-text-xs);
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.style-panel-button-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.style-panel-button-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.style-panel-button-secondary {
  background-color: transparent;
  color: #d1d5db;
  border: 1px solid rgba(115, 115, 115, 0.3);
}

.style-panel-button-secondary:hover {
  background-color: rgba(38, 38, 38, 0.5);
  color: white;
  border-color: rgba(115, 115, 115, 0.5);
}

/* 优化标签页 */
.style-panel-tabs {
  display: flex;
  border-bottom: 1px solid rgba(115, 115, 115, 0.3);
  background-color: rgba(38, 38, 38, 0.3);
}

.style-panel-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.375rem;
  font-size: var(--panel-text-xs);
  font-weight: 500;
  color: #9ca3af;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
}

.style-panel-tab:hover {
  color: white;
  background-color: rgba(115, 115, 115, 0.2);
}

.style-panel-tab.active {
  color: #3b82f6;
  background-color: rgba(23, 23, 23, 0.5);
}

.style-panel-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3b82f6;
}

/* 优化属性项 */
.style-panel-property {
  margin-bottom: 0.5rem;
}

.style-panel-property-label {
  font-size: var(--panel-text-xs);
  font-weight: 500;
  color: #d1d5db;
  margin-bottom: 0.25rem;
  display: block;
}

/* 优化预设选择器 */
.style-panel-preset-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.style-panel-preset-item {
  padding: 0.125rem 0.375rem;
  font-size: 0.625rem;
  background-color: rgba(38, 38, 38, 0.5);
  border: 1px solid rgba(115, 115, 115, 0.3);
  border-radius: 0.25rem;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.15s ease;
}

.style-panel-preset-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.style-panel-preset-item.active {
  background-color: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 快速样式按钮优化 */
.style-panel-quick-button {
  padding: 0.5rem;
  font-size: var(--panel-text-xs);
  background-color: rgba(38, 38, 38, 0.3);
  border: 1px solid rgba(115, 115, 115, 0.3);
  border-radius: 0.375rem;
  color: #d1d5db;
  cursor: pointer;
  transition: all 0.15s ease;
  text-align: left;
}

.style-panel-quick-button:hover {
  background-color: rgba(115, 115, 115, 0.3);
  border-color: rgba(59, 130, 246, 0.3);
  color: white;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .style-panel-container {
    --panel-spacing: 0.375rem;
    --panel-item-height: 1.25rem;
    --panel-text-sm: 0.625rem;
    --panel-text-xs: 0.5625rem;
  }
  
  .style-panel-number-adjuster input {
    width: 2rem;
  }
  
  .style-panel-button {
    height: 1.5rem;
    padding: 0 0.375rem;
  }
}

/* 关键属性面板紧凑样式 */
.style-panel-key-property {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.style-panel-key-label {
  width: 2rem;
  flex-shrink: 0;
  font-size: 0.625rem;
  font-weight: 500;
  color: #d1d5db;
}

.style-panel-key-input {
  flex: 1;
  min-width: 0;
}

.style-panel-key-color-input {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 0.25rem;
  border: 1px solid rgba(115, 115, 115, 0.3);
  cursor: pointer;
  transition: all 0.15s ease;
}

.style-panel-key-color-input:hover {
  transform: scale(1.1);
  border-color: rgba(59, 130, 246, 0.5);
}

.style-panel-key-number-adjuster {
  display: flex;
  align-items: center;
  background-color: rgba(38, 38, 38, 0.5);
  border: 1px solid rgba(115, 115, 115, 0.3);
  border-radius: 0.25rem;
  overflow: hidden;
}

.style-panel-key-number-adjuster button {
  padding: 0.125rem;
  background-color: transparent;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.style-panel-key-number-adjuster button:hover {
  background-color: rgba(115, 115, 115, 0.3);
  color: white;
}

.style-panel-key-number-adjuster input {
  width: 2rem;
  height: 1.25rem;
  background-color: transparent;
  border: none;
  color: white;
  text-align: center;
  font-size: 0.625rem;
}

.style-panel-key-number-adjuster input:focus {
  outline: none;
}

.style-panel-quick-colors {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.style-panel-quick-color {
  width: 1rem;
  height: 1rem;
  border-radius: 0.25rem;
  border: 1px solid rgba(115, 115, 115, 0.3);
  cursor: pointer;
  transition: all 0.15s ease;
}

.style-panel-quick-color:hover {
  transform: scale(1.1);
  border-color: rgba(59, 130, 246, 0.5);
}

/* 优化选择器在关键面板中的显示 */
.style-panel-key-select {
  height: 1.25rem;
  font-size: 0.625rem;
  background-color: rgba(38, 38, 38, 0.5);
  border: 1px solid rgba(115, 115, 115, 0.3);
  color: white;
}

.style-panel-key-select:focus {
  border-color: rgba(59, 130, 246, 0.5);
  outline: none;
}

/* 紧凑输入框 */
.style-panel-compact-input {
  height: 1.25rem;
  font-size: 0.625rem;
  padding: 0.125rem 0.25rem;
  background-color: rgba(38, 38, 38, 0.5);
  border: 1px solid rgba(115, 115, 115, 0.3);
  border-radius: 0.25rem;
  color: white;
  transition: all 0.15s ease;
}

.style-panel-compact-input:focus {
  border-color: rgba(59, 130, 246, 0.5);
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.style-panel-compact-input::placeholder {
  color: #6b7280;
}

/* =================================
   AI输入框底部工具栏样式
   ================================= */

/* 底部工具栏 - 左右分布式布局 */
.ai-input-bottom-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  bottom: 0.5rem;
  left: 0.5rem;
  right: 0.5rem;
  z-index: 10;
}

/* 左侧功能按钮组样式 */
.ai-input-left-function-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* 专业级功能按钮 - 带文字标签 */
.ai-input-pro-button {
  padding: 0.375rem 0.75rem;
  height: 2rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  background: transparent;
  position: relative;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 图片上传按钮 - 专业级 */
.ai-input-pro-image {
  color: #6b7280;
}

.ai-input-pro-image:hover {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  transform: scale(1.05);
}

.ai-input-pro-image:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* MCP按钮 - 专业级 */
.ai-input-pro-mcp {
  color: #6b7280;
}

.ai-input-pro-mcp:hover {
  color: #8b5cf6;
  background: rgba(139, 92, 246, 0.1);
  transform: scale(1.05);
}

.ai-input-pro-mcp.active {
  color: #8b5cf6;
  background: rgba(139, 92, 246, 0.15);
  box-shadow: 0 0 0 1px rgba(139, 92, 246, 0.2);
}

.ai-input-pro-mcp:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 专业级分隔线 */
.ai-input-pro-divider {
  width: 1px;
  height: 1.5rem;
  background: rgba(107, 114, 128, 0.3);
  margin: 0 0.25rem;
}

/* 欢迎模式下的分隔线 */
.welcome-mode .ai-input-pro-divider {
  background: rgba(255, 255, 255, 0.2);
}

/* 专业级发送按钮 */
.ai-input-pro-send {
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  font-weight: 500;
}

.ai-input-pro-send.enabled {
  background: #3b82f6;
  color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ai-input-pro-send.enabled:hover {
  background: #2563eb;
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.ai-input-pro-send.disabled {
  background: #6b7280;
  color: #9ca3af;
  cursor: not-allowed;
}

.ai-input-pro-send:active {
  transform: scale(0.95);
}

/* 欢迎模式下的按钮调整 */
.welcome-mode .ai-input-pro-button {
  color: rgba(255, 255, 255, 0.6);
}

.welcome-mode .ai-input-pro-image:hover {
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.15);
}

.welcome-mode .ai-input-pro-mcp:hover {
  color: #a78bfa;
  background: rgba(167, 139, 250, 0.15);
}

.welcome-mode .ai-input-pro-mcp.active {
  color: #a78bfa;
  background: rgba(167, 139, 250, 0.2);
  box-shadow: 0 0 0 1px rgba(167, 139, 250, 0.3);
}

/* =================================
   图片上传和快捷提示词样式
   ================================= */

/* 拖拽状态样式 */
.ai-input-container.dragging {
  border-color: #3b82f6 !important;
  background: rgba(59, 130, 246, 0.05) !important;
  transform: scale(1.01);
}

.ai-input-container.dragging::before {
  content: '拖拽图片到此处上传';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 500;
  pointer-events: none;
  z-index: 20;
}

/* 图片预览样式 - 直接渲染图片 */
.ai-input-image-preview {
  width: 5rem;
  height: 5rem;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid rgba(115, 115, 115, 0.3);
  background: rgba(38, 38, 38, 0.9);
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
  position: relative;
}

.ai-input-image-preview:hover {
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.ai-input-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.ai-input-image-preview:hover img {
  transform: scale(1.05);
}

/* 图片图标容器 */
.ai-input-image-icon {
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 快捷提示词按钮样式 - 代码生成专用 */
.ai-input-quick-prompt {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  white-space: nowrap;
  border: 1px solid;
  font-weight: 500;
  backdrop-filter: blur(12px);
  position: relative;
  overflow: hidden;
}

.ai-input-quick-prompt::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.5s ease;
}

.ai-input-quick-prompt:hover::before {
  left: 100%;
}

/* 普通模式快捷提示词 */
.ai-input-quick-prompt.normal {
  background: rgba(64, 64, 64, 0.9);
  border-color: rgba(115, 115, 115, 0.4);
  color: #e5e7eb;
}

.ai-input-quick-prompt.normal:hover {
  background: rgba(82, 82, 82, 0.95);
  border-color: rgba(59, 130, 246, 0.6);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* 欢迎模式快捷提示词 */
.ai-input-quick-prompt.welcome {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

.ai-input-quick-prompt.welcome:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

/* 快捷提示词禁用状态 */
.ai-input-quick-prompt:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 图片移除按钮 */
.ai-input-remove-image {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: #ef4444;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.15s ease;
  opacity: 0;
}

.ai-input-image-card:hover .ai-input-remove-image {
  opacity: 1;
}

.ai-input-remove-image:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #dc2626;
  transform: scale(1.1);
}

/* 图片预览区域动画 */
.ai-input-image-preview-enter {
  animation: imagePreviewSlideIn 0.3s ease-out;
}

@keyframes imagePreviewSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 快捷提示词区域动画 */
.ai-input-quick-prompts-enter {
  animation: quickPromptsSlideIn 0.4s ease-out 0.1s both;
}

@keyframes quickPromptsSlideIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 快捷提示词容器样式 - 避免遮挡 */
.ai-input-quick-prompts-container {
  position: absolute;
  top: 7rem; /* 112px - 给图片预览留空间 */
  left: 0.75rem;
  right: 0.75rem;
  bottom: 3.5rem; /* 56px - 给底部工具栏留空间 */
  z-index: 10;
  overflow-y: auto;
  pointer-events: none; /* 容器本身不拦截事件 */
}

.ai-input-quick-prompts-container > * {
  pointer-events: auto; /* 子元素可以接收事件 */
}

/* 图片预览卡片样式 */
.ai-input-image-preview {
  background: rgba(38, 38, 38, 0.8);
  border: 1px solid rgba(115, 115, 115, 0.3);
  border-radius: 0.5rem;
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
}

.ai-input-image-preview:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(38, 38, 38, 0.9);
}

/* 图片预览项样式 */
.ai-input-image-item {
  background: rgba(64, 64, 64, 0.6);
  border: 1px solid rgba(115, 115, 115, 0.2);
  border-radius: 0.375rem;
  transition: all 0.15s ease;
}

.ai-input-image-item:hover {
  background: rgba(64, 64, 64, 0.8);
  border-color: rgba(59, 130, 246, 0.3);
}

/* 移除按钮样式 */
.ai-input-remove-button {
  color: #ef4444;
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1;
  cursor: pointer;
  transition: all 0.15s ease;
}

.ai-input-remove-button:hover {
  color: #dc2626;
  transform: scale(1.1);
}

/* 功能按钮工具提示样式 */
.ai-input-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 50;
}

.ai-input-function-button:hover .ai-input-tooltip {
  opacity: 1;
}

/* 欢迎模式下的按钮样式调整 */
.welcome-mode .ai-input-function-button {
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
}

.welcome-mode .ai-input-image-button:hover {
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.2);
}

.welcome-mode .ai-input-mcp-button:hover {
  color: #a78bfa;
  background: rgba(167, 139, 250, 0.2);
}

.welcome-mode .ai-input-mcp-button.active {
  color: #a78bfa;
  background: rgba(167, 139, 250, 0.25);
  box-shadow: 0 0 0 1px rgba(167, 139, 250, 0.4);
}

/* AI输入框安全区域样式 - 防止文字覆盖 */
.ai-input-safe-zone {
  /* 确保文字不会覆盖图片和按钮区域 */
  margin-top: 0;
  margin-bottom: 0;
  box-sizing: border-box;
}

/* 有图片时的特殊文字安全区域 */
.ai-input-with-images {
  /* 扩大文字显示区域 - 更贴近边界，可向右扩展 */
  position: relative;
  
  /* 创建一个更大的文字容器 - 向右扩展 */
  width: calc(100% - 3rem) !important; /* 右侧只为发送按钮预留3rem，大幅增加输入长度 */
  min-height: calc(100% - 4rem) !important; /* 减少上下预留空间 */
  max-height: calc(100vh - 6rem) !important; /* 允许向上扩展 */
  
  /* 使用相对布局，保持与图片的专业距离 */
  margin-top: 7rem !important; /* 112px - 与图片保持专业距离 */
  margin-left: 1rem !important; /* 16px - 左侧正常间距 */
  margin-right: 2rem !important; /* 32px - 只为发送按钮预留空间 */
  margin-bottom: 3.5rem !important; /* 56px - 往上调整，距离底部工具栏 */
  
  /* 内部间距 */
  padding: 0.75rem !important;
  
  /* 隐藏边框 - 使用背景色 */
  border: none !important;
  background: transparent !important;
  outline: none !important;
  
  /* 动态高度控制 */
  box-sizing: border-box;
  overflow: visible; /* 允许向上扩展 */
  overflow-y: auto; /* 允许垂直滚动 */
  
  /* 文字换行控制 */
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  
  /* 动态扩展动画 */
  transition: all 0.3s ease;
  
  /* 滚动条样式 - 完全隐藏AI输入框滚动条 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.ai-input-with-images::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.ai-input-with-images::-webkit-scrollbar-track {
  display: none !important;
}

.ai-input-with-images::-webkit-scrollbar-thumb {
  display: none !important;
}

/* 可视化边界指示 - 帮助用户理解文字安全区域 */
.ai-input-with-images::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 1px dashed rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  pointer-events: none;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ai-input-with-images:focus::before {
  opacity: 1;
}

/* 无图片时的正常文字区域 */
.ai-input-without-images {
  padding: 1rem !important;
  padding-bottom: 4rem !important; /* 64px - 为底部工具栏预留空间 */
  border: none !important;
  background: transparent !important;
  outline: none !important;
}

/* 隐藏输入框边框 - 全局设置 */
.ai-input-safe-zone {
  border: none !important;
  background: transparent !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 响应式调整 - 底部工具栏 */
@media (max-width: 768px) {
  .ai-input-bottom-toolbar {
    left: 0.375rem;
    right: 0.375rem;
    bottom: 0.375rem;
  }
  
  .ai-input-left-function-group {
    gap: 0.125rem;
  }
  
  .ai-input-pro-button {
    width: 1.75rem;
    height: 1.75rem;
  }
  
  .ai-input-pro-send {
    width: 1.75rem;
    height: 1.75rem;
  }
  
  .ai-input-image-preview {
    padding: 0.75rem;
  }
  
  .ai-input-image-item {
    padding: 0.375rem;
    font-size: 0.75rem;
  }
  
  /* 移动端安全区域调整 */
  .ai-input-with-images {
    width: calc(100% - 1.5rem) !important; /* 移动端右侧大幅减少预留空间 */
    min-height: calc(100% - 6rem) !important; /* 移动端高度调整 */
    max-height: calc(100vh - 6rem) !important; /* 允许向上扩展 */
    margin-top: 5rem !important; /* 80px - 移动端与图片保持专业距离 */
    margin-left: 0.5rem !important; /* 8px - 左侧间距 */
    margin-right: 1rem !important; /* 16px - 右侧为发送按钮预留 */
    margin-bottom: 3rem !important; /* 48px - 往上调整，距离底部工具栏 */
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .ai-input-bottom-toolbar {
    left: 0.25rem;
    right: 0.25rem;
    bottom: 0.25rem;
  }
  
  .ai-input-pro-button {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .ai-input-pro-send {
    width: 1.5rem;
    height: 1.5rem;
  }
}

/* 动态扩展样式 - 文字输入时图片区域上移 */
.ai-input-expanded {
  /* 文字区域向上扩展时的样式 - 保持专业距离 */
  margin-top: 5rem !important; /* 80px - 图片区域上移后仍保持专业距离 */
  margin-left: 1rem !important; /* 16px - 左侧间距 */
  margin-right: 1.5rem !important; /* 24px - 更贴近发送按钮 */
  margin-bottom: 3.5rem !important; /* 56px - 保持底部位置 */
  
  width: calc(100% - 2.5rem) !important; /* 向右进一步扩展 */
  min-height: calc(100% - 3rem) !important; /* 增加可用高度 */
}

.ai-input-expanded .ai-input-image-preview {
  /* 图片预览区域上移 */
  top: 0.5rem !important; /* 8px - 更贴近顶部 */
  transform: translateY(-2rem); /* 向上偏移 */
  transition: all 0.3s ease;
}

.ai-input-expanded .ai-input-quick-prompts-container {
  /* 快捷提示词区域相应调整 */
  top: 4rem !important; /* 64px - 跟随图片区域调整 */
  transition: all 0.3s ease;
}

/* 文字输入时的动态高度调整 */
.ai-input-dynamic-height {
  /* 根据内容动态调整高度 */
  height: auto !important;
  min-height: 3rem !important; /* 48px - 最小高度 */
  max-height: calc(100vh - 6rem) !important; /* 最大高度限制 */
  resize: vertical; /* 允许垂直调整 */
}

/* 图片区域动态位置调整 */
.ai-input-image-container-dynamic {
  /* 图片容器动态定位 - 使用相对布局保持在容器内 */
  position: absolute;
  top: 1rem; /* 16px - 保持在容器内部 */
  left: 0.75rem;
  transition: all 0.3s ease;
  z-index: 15;
}

.ai-input-image-container-dynamic.moved-up {
  /* 图片容器上移状态 - 限制在容器内 */
  top: 0.5rem; /* 8px - 上移但保持在容器内 */
}

/* 快捷提示词动态位置调整 */
.ai-input-quick-prompts-dynamic {
  /* 快捷提示词动态定位 - 固定在图片右侧，保持在容器内 */
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.25rem; /* 4px - 与图片顶部对齐，保持在容器内 */
  transition: all 0.3s ease;
  z-index: 10;
}

.ai-input-quick-prompts-dynamic.moved-up {
  /* 快捷提示词上移状态 - 跟随图片移动但保持在容器内 */
  margin-top: 0; /* 上移时减少顶部间距 */
}

/* 文字输入焦点时的视觉反馈 */
.ai-input-with-images:focus {
  /* 聚焦时的视觉增强 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  border-radius: 0.5rem;
}

/* 响应式动态调整 */
@media (max-width: 768px) {
  .ai-input-expanded {
    margin-top: 3rem !important; /* 48px - 移动端上移后仍保持专业距离 */
    margin-left: 0.5rem !important; /* 8px - 左侧间距 */
    margin-right: 0.5rem !important; /* 8px - 更贴近发送按钮 */
    margin-bottom: 3rem !important; /* 48px - 往上调整，保持底部位置 */
    width: calc(100% - 1rem) !important; /* 移动端进一步向右扩展 */
  }
  
  .ai-input-image-container-dynamic {
    top: 0.5rem; /* 8px - 移动端保持在容器内 */
  }
  
  .ai-input-image-container-dynamic.moved-up {
    top: 0.25rem; /* 4px - 移动端上移但保持在容器内 */
  }
  
  .ai-input-quick-prompts-dynamic {
    margin-top: 0.125rem; /* 2px - 移动端与图片对齐 */
  }
  
  .ai-input-quick-prompts-dynamic.moved-up {
    margin-top: 0; /* 移动端上移时减少间距 */
  }
}

/* 🎯 欢迎页面滚动优化已移除 - 使用全局body滚动条 */

/* 🎯 页面基础设置已移至统一滚动条样式部分 */

/* 🎯 社区页面自定义滚动条 - 深色简洁风格 */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px !important;
  background: transparent !important;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(115, 115, 115, 0.6) !important;
  border-radius: 2px !important;
  transition: background-color 0.2s ease !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(115, 115, 115, 0.8) !important;
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent !important;
}

/* Firefox 滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin !important;
  scrollbar-color: rgba(115, 115, 115, 0.6) transparent !important;
}

/* 🎯 这些样式已被统一的滚动条样式替代 */

/* 隐藏社区卡片iframe的滚动条 */
.community-card iframe {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.community-card iframe::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 通用iframe滚动条隐藏 */
iframe {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

iframe::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}



/* 🎯 移除了隐藏滚动条的规则，现在使用统一的滚动条样式 */

/* 🎯 页面基础滚动设置 - 首页欢迎界面隐藏主滚动条 */
html {
  overflow-x: hidden !important; /* 防止水平滚动条 */
  overflow-y: hidden !important; /* 隐藏主页面滚动条 */
}

body {
  overflow-x: hidden !important; /* 防止水平滚动条 */
  overflow-y: hidden !important; /* 隐藏主页面滚动条 */
}

/* 🎯 只隐藏iframe滚动条，保留其他滚动条 */
iframe::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

iframe {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE */
}

/* 🎨 统一的系统滚动条样式 - 现代暗色主题 */

/* 🎯 全局滚动条样式 - 适用于所有元素 */
* {
  scrollbar-width: thin !important;
  scrollbar-color: rgba(156, 163, 175, 0.5) rgba(0, 0, 0, 0.05) !important;
}

/* 🎯 深色模式滚动条颜色 */
.dark * {
  scrollbar-color: rgba(55, 65, 81, 0.8) transparent !important;
}

/* 🎯 Webkit浏览器滚动条样式 */
::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
  background: transparent !important;
  display: block !important; /* 强制显示 */
}

::-webkit-scrollbar-track {
  background: rgba(156, 163, 175, 0.1) !important;
  border-radius: 3px !important;
  display: block !important; /* 强制显示 */
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5) !important;
  border-radius: 3px !important;
  transition: background-color 0.2s ease !important;
  display: block !important; /* 强制显示 */
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7) !important;
}

::-webkit-scrollbar-corner {
  background: transparent !important;
  display: block !important; /* 强制显示 */
}

/* 🎯 深色模式下的滚动条样式 */
.dark ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1) !important;
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(55, 65, 81, 0.8) !important;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.9) !important;
}

/* 🎯 主页面滚动条 - 更细更优雅 */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  width: 4px !important;
  background: transparent !important;
  display: block !important; /* 强制显示 */
}

html::-webkit-scrollbar-track,
body::-webkit-scrollbar-track {
  background: rgba(156, 163, 175, 0.1) !important;
  border-radius: 2px !important;
  display: block !important; /* 强制显示 */
}

html::-webkit-scrollbar-thumb,
body::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4) !important;
  border-radius: 2px !important;
  transition: background-color 0.2s ease !important;
  display: block !important; /* 强制显示 */
}

html::-webkit-scrollbar-thumb:hover,
body::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.6) !important;
}

/* 🎯 深色模式下的主页面滚动条 */
.dark html::-webkit-scrollbar-track,
.dark body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05) !important;
}

.dark html::-webkit-scrollbar-thumb,
.dark body::-webkit-scrollbar-thumb {
  background: rgba(55, 65, 81, 0.6) !important;
}

.dark html::-webkit-scrollbar-thumb:hover,
.dark body::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.8) !important;
}

/* 🎯 侧边栏滚动条 - 深色主题 */
.project-sidebar-scroll::-webkit-scrollbar {
  width: 4px;
  background: rgba(0, 0, 0, 0.1);
}

.project-sidebar-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.project-sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.project-sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.6);
}

/* 🎯 聊天区域滚动条 - 精细样式 */
.chat-scrollbar::-webkit-scrollbar {
  width: 3px;
  background: transparent;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 🎯 代码编辑器滚动条 - VSCode风格 */
.vscode-scrollbar::-webkit-scrollbar {
  width: 8px;
  background: rgba(0, 0, 0, 0.1);
}

.vscode-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.vscode-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.6);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* 🎯 社区项目网格滚动条 - 简洁风格 */
.community-scroll::-webkit-scrollbar {
  width: 5px;
  background: transparent;
}

.community-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.community-scroll::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.4);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.community-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.6);
}

/* 🚀 拖拽性能优化模式 */
:root[style*="--drag-performance-mode"] * {
  transition: none !important;
  animation: none !important;
  transform-style: flat !important;
}

:root[style*="--drag-performance-mode"] iframe {
  will-change: transform;
  contain: layout style paint;
  pointer-events: none;
}

:root[style*="--drag-performance-mode"] .preview-container-optimized {
  will-change: transform;
  contain: layout style paint;
}

/* 🚀 代码编辑器特殊优化 */
:root[style*="--drag-performance-mode"] .code-scroll-optimized {
  will-change: transform, contents;
  contain: layout style paint size;
  transform-style: preserve-3d;
}

:root[style*="--drag-performance-mode"] .code-scroll-optimized[data-drag-optimized="true"] {
  /* 暂停语法高亮和其他计算密集操作 */
  pointer-events: none;
  user-select: none;
}

/* 🚀 全局动画禁用 */
:root[style*="--disable-animations"] * {
  transition-duration: 0s !important;
  animation-duration: 0s !important;
  animation-delay: 0s !important;
}

/* 针对预览区域的特殊优化 */
.preview-container-optimized {
  contain: style;
}

.iframe-scroll-optimized {
  contain: style layout;
  will-change: auto;
}

/* GPU加速优化 */
.critical-optimized {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 🎯 拖拽条专业样式优化 */
.resizable-splitter-optimized {
  /* 启用硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  
  /* 优化重绘性能 */
  contain: layout style;
  
  /* 消除潜在的性能问题 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 拖拽时的全局优化 */
.dragging-active {
  /* 全局禁用文本选择 */
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  
  /* 禁用上下文菜单 */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 高性能拖拽指示器 */
.drag-indicator-optimized {
  will-change: opacity, transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 针对Monaco编辑器等的特殊优化 */
.monaco-editor.drag-optimized {
  pointer-events: none !important;
  contain: layout style paint !important;
}

/* 🎯 代码编辑器优化样式 */
.vscode-scrollbar {
  /* VS Code风格的滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #424242 #1e1e1e;
}

.vscode-scrollbar::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.vscode-scrollbar::-webkit-scrollbar-track {
  background: #1e1e1e;
}

.vscode-scrollbar::-webkit-scrollbar-thumb {
  background: #424242;
  border: 3px solid #1e1e1e;
  border-radius: 7px;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #4f4f4f;
}

.vscode-scrollbar::-webkit-scrollbar-corner {
  background: #1e1e1e;
}

/* 代码编辑器内容优化 */
.vscode-code {
  contain: layout style;
  will-change: scroll-position;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.code-scroll-optimized {
  /* 优化滚动性能 */
  contain: layout style paint;
  overflow-anchor: none;
  scroll-behavior: auto;
}

.memory-optimized {
  /* 内存使用优化 */
  contain: layout style paint;
}

/* textarea编辑器优化 */
.code-editor-textarea {
  /* 禁用浏览器默认的拼写检查和自动完成 */
  spellcheck: false;
  autocomplete: off;
  autocorrect: off;
  autocapitalize: off;
  
  /* 优化文本渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
  
  /* 性能优化 */
  contain: layout style;
  will-change: contents;
  
  /* 移除默认样式 */
  border: none;
  outline: none;
  resize: none;
  background: transparent;
  
  /* 确保字体一致性 */
  font-family: 'Cascadia Code', 'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, Consolas, monospace;
  font-variant-ligatures: none;
  font-feature-settings: normal;
}

/* 编辑模式状态指示器 */
.edit-mode-indicator {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 代码行号对齐优化 */
.line-numbers-container {
  /* 确保行号容器的性能 */
  contain: layout style paint;
  overflow: hidden;
  will-change: scroll-position;
}

/* 代码内容容器优化 */
.code-content-container {
  contain: layout style;
  overflow-anchor: none;
}

/* 编辑提示浮层 */
.edit-hint-overlay {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  contain: layout style paint;
}

/* 高对比度代码主题优化 */
@media (prefers-contrast: high) {
  .vscode-scrollbar::-webkit-scrollbar-thumb {
    background: #666666;
  }
  
  .vscode-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #888888;
  }
}

/* 🎯 缩进指示线优化 */
.indent-guide-line {
  transition: opacity 0.2s ease-in-out;
  will-change: opacity;
}

.indent-guide-line:hover {
  opacity: 0.9 !important;
}

/* 代码编辑器悬停时突出显示缩进线 */
.code-content-container:hover .indent-guide-line {
  opacity: 0.7;
}

/* 为不同缩进级别提供不同的颜色深度 */
.indent-guide-level-0 { opacity: 0.3; }
.indent-guide-level-1 { opacity: 0.4; }
.indent-guide-level-2 { opacity: 0.5; }
.indent-guide-level-3 { opacity: 0.6; }

/* 活跃缩进线（用户正在编辑的缩进级别） */
.indent-guide-active {
  background-color: #569cd6 !important;
  opacity: 0.8 !important;
}

/* 缩进指示线在暗色主题下的优化 */
@media (prefers-color-scheme: dark) {
  .indent-guide-line {
    background-color: #404040;
  }
}

/* 缩进指示线在亮色主题下的优化 */
@media (prefers-color-scheme: light) {
  .indent-guide-line {
    background-color: #d0d0d0;
  }
}

/* 🎯 现代化聊天界面样式 */
.message-wrapper {
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 用户消息气泡悬停效果 */
.message-wrapper .bg-blue-500 {
  transition: all 0.2s ease;
}

.message-wrapper .bg-blue-500:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* AI消息气泡悬停效果 */
.message-wrapper .bg-neutral-800 {
  transition: all 0.2s ease;
}

.message-wrapper .bg-neutral-800:hover {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  border-color: #4b5563;
}

/* 头像悬停效果 */
.message-wrapper .rounded-full {
  transition: all 0.2s ease;
}

.message-wrapper .rounded-full:hover {
  transform: scale(1.05);
}

/* AI头像特殊效果 */
.message-wrapper .bg-neutral-900.border-neutral-700:hover {
  border-color: #06d6a0;
  box-shadow: 0 0 8px rgba(6, 214, 160, 0.3);
}

/* 版本标签动画 */
.bg-cyan-400\/10 {
  transition: all 0.2s ease;
}

.bg-cyan-400\/10:hover {
  background: rgba(34, 211, 238, 0.2);
  transform: scale(1.05);
}

/* 聊天滚动条优化 */
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #4b5563 transparent;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 3px;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 操作按钮组优化 */
.message-wrapper button {
  transition: all 0.15s ease;
}

.message-wrapper button:hover {
  transform: translateY(-1px);
}

/* 消息时间戳淡入动画 */
.message-wrapper span {
  animation: fadeIn 0.5s ease-out 0.2s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式布局优化 */
@media (max-width: 640px) {
  .message-wrapper .max-w-\[80\%\] {
    max-width: 90%;
  }
  
  .message-wrapper .px-4 {
    padding-left: 12px;
    padding-right: 12px;
  }
}

/* 用户头像优化样式 */
.user-avatar-container {
  position: relative;
  transition: all 0.2s ease;
}

.user-avatar-container:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.user-avatar-image {
  transition: opacity 0.2s ease;
}

.user-avatar-image:hover {
  opacity: 0.9;
}

/* 头像加载失败时的样式 */
.avatar-fallback {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* AI头像容器优化 */
.ai-avatar-container {
  transition: all 0.2s ease;
  position: relative;
}

.ai-avatar-container:hover {
  border-color: #06d6a0;
  box-shadow: 0 0 12px rgba(6, 214, 160, 0.4);
  transform: scale(1.05);
}

.ai-avatar-container::after {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  padding: 2px;
  background: linear-gradient(45deg, #06d6a0, #22d3ee);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: subtract;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.ai-avatar-container:hover::after {
  opacity: 0.5;
}

/* VS Code风格代码编辑器优化 */
.vscode-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(121, 121, 121, 0.4) transparent;
}

.vscode-scrollbar::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.vscode-scrollbar::-webkit-scrollbar-corner {
  background-color: transparent;
}

.vscode-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(121, 121, 121, 0.4);
  border: 3px solid transparent;
  border-radius: 7px;
  background-clip: content-box;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(121, 121, 121, 0.7);
}

.vscode-scrollbar::-webkit-scrollbar-thumb:active {
  background-color: rgba(121, 121, 121, 0.8);
}

.vscode-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 深色模式滚动条优化 */
.dark .vscode-scrollbar {
  scrollbar-color: rgba(121, 121, 121, 0.4) transparent;
}

.dark .vscode-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(121, 121, 121, 0.4);
}

.dark .vscode-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(121, 121, 121, 0.7);
}

/* 代码行智能换行优化 */
.code-line {
  position: relative;
  contain: layout style;
  will-change: auto;
}

/* 确保换行内容在缩进指示线右侧 */
.code-line > div {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: none;
}

/* 防止换行内容超出边界 */
.code-line span[style*="whiteSpace"] {
  max-width: 100%;
  box-sizing: border-box;
}

/* 缩进指示线动画优化 */
.code-line:hover > div[class*="absolute"] {
  opacity: 0.6 !important;
  transition: opacity 0.15s ease;
}

/* 空行的缩进指示线保持连续但稍微淡化 */
.code-line[data-empty="true"] > div[class*="absolute"] {
  opacity: 0.15 !important;
}

.code-line[data-empty="true"]:hover > div[class*="absolute"] {
  opacity: 0.3 !important;
}

/* 代码文本选择优化 */
.vscode-code {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 代码字体渲染优化 */
.vscode-code,
.code-line,
.code-line span {
  font-feature-settings: "liga" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 智能换行行号对齐 */
.code-line-number {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: inherit;
}

/* 性能优化：减少重绘 */
.code-editor-container {
  contain: layout style paint;
  transform: translateZ(0);
  will-change: scroll-position;
}
